7/3/2025:
- Queue observations:
	+ Pairwise order implies total order
	+ Tighten bounds is necessary
	+ Condition: Push < all < Pop
	+ Elem Order: A can go before B iff all A.left < B.right

	-> Greedy algorithm, select the first element
		. Proof by shifting correct partial history to bound
		. Correct because (push < all < pop) is satisfied

16/2/2025:
- Mistake: Split needs to check that the split is not sandwiched

11/2/2025: 
Meeting:
- Implement base algorithm
- Testing:
	+ 3 threads, invoke/response bound, push vs. pop ratio (4:1, >> 1, ~1, peeks), 
	+ 10 operations over 3 threads, random start/end times
	+ number of operations can overlap: random operations / expand time ranges
	+ generation: total order (good for correct generations) / operations
	+ C++ implementation

10/2/2025:
Brainstormed ideas
- (l, r), 1: At m, (l, m) + (m, r) -> (l, r)
- (l, r), 2: Segment tree technique to eliminate (l ... [] ... r .. [])
- Speed up peeks: Peek is good if (push.upper < m ==> pop.lower <= m)
- (l, r), 3: inc l to push.upper, dec r to pop.lower

- Two cases:
	+ Tree like structure
	+ Random alternative push/pop
- (l, r), 4: Push l, possible jumps are determined (must be at a pop, include last operation) -> range
	+ Valid (l, r)s are limited, because too easily **cross**ed
- m search: if can m then always m, when (push.upper >= m || pop.lower <= m \forall operations)
- NECESSARY: sliding window, (like (l, r), 2)

2/2/2025:
Chat log:
	"Handling of the peeks are as follow:
	- We recursively assign time to peeks from left to right
	- At each step of the recursion, assume that the last peek as put at m, we can infer what peeks are left (need to have lower > m)
	- If all peeks are done, we check if the last interval can by "solve_interval_restriction"
	- Otherwise, we try to assign time to the next peek
	- Check if solve_interval_restriction applies to the interval between two peeks
	- Recursively continue to put peeks after that
	- Use memoization to reduce recalculation"

	"

31/1/2025:
Equivalence of arc <-> operations
+ Difficult to come up with something != going between segments / different ideas need to address this
+ Order at equal points not matter, can be constructed from time


- Presentation: equivalence operations <-> arc ==> reasoning
- Correctness of construct peeks: (p1, p2, ..., pk) true iff exist allocation of rest -> proof that it's unique

30/1/2025:
New invariant used to prove:
OKAY_INTERVAL(l, r) = OKAY_INTERVAL(l, m1) && OKAY_INTERVAL(m1, m2) && ... && OKAY_INTERVAL(mk, r)
	+ m1, m2, ..., mk are the points in the middle of l and r
	+ This invariant is used to prove the correctness of the algorithm

18/10/2024
- Heuristic: Segments are generally short
- Other ideas: 
	+ If not fix exact time -> Rely on partial order -> element by element (pairwise relation?) -> Faulty

----------

25/9/2024
Key idea: Stack interference <-> Segments

----------

19/7/2024
- Make more closer to what ppl expect to read -> call and return index
- Partially ordered set of operations -> can it be generalizable? simple algorithm?
- Interval view? ideas of simplification? -> implementation?
- Express things about specification? imperative -> more functional (e.g. for loops -> for each logic)
- Algo (interval) -> we get
- http://www.lix.polytechnique.fr/~cenea/papers/pldi2015.pdf

----------

testing concurrent objects

focus on stack, queue, double-ended queue




highllev le
  

  exectution, 

  no exact point, when called, when returned, 

  execution is correct?

  point of view, linearizibiliy

  -> technical terms each method, time interval, each method -> point in time

  look in order in poitn int time, sequential point of view, must satisfy everythine

  known to be NP complete,but for certain data structure?

  -> challenging

  -> for stack, queue, double-ended is P

  -> more things -> more complex

  -> results

 Phillip B. Gibbons, Ephriam Korach, 
 Testing Shared Memoties. SIAM j. Comput. 26(4): 1208-1244 (1997)

 Conclusion:

= Wrap up, what has been shown, perspective, (queue is simpler), polynomial only,
optimizations availale. 



Title: Linearization for Stacks, Queue, Deque

I. Intro
- what is linearizability? sequential execution?
	+ interleaviAlsongs
- why helpful?
- related work
- ADT: peek
- queue? set?
- each object pushed / popped exactly one, label appear one
- a. stack with peek
- b. deque with peek
- presented wholly, can simplify to without peeks
II Stack with peeks
III Double-ended q w peks

TODO: Add execution history() Explicit flow of exection: (items, operations, lower, upper)
	  Return linearization()
	  linearization is linearization point (not real world). to explain correctness of algorithm
	  give high-level explanation at beginning
	  	people should know what they are reading before they are reading it
	  	(2.4 -> hierarchy, sequential of items become structure)
	  	(2.6 -> extend linearization)
	  	(2.7 -> peeks, split into subproblems, solve separately)
	  	(2.8 -> main algorithm)
	  	give more accurate description for algorithms
	  	(3.3 -> algorithm for without stack items, restructure top-down, start with entry point)  
	  	(3.4 -> specify similar, explicitly say that just replace A with B)

Change the order (2.8 -> 2.7 -> 2.6) (2.9 in conclusion)

Conclusion
- complexity
- possible optimizations
- complexity of correctness, sketch only, possible that minor details are omitted
- power: list all sequential history
- disregards peeks, practical application for stacks
- ideas (assign to ends), create heuristics
- build powerful verification tools

References
1. 
2.
3. 

-----------------

- Queue
	+ Greedy algorithm (choose first elements)
	+ 2D interpretation (L, R)
	+ Theorem: Exist partial orders of objects (not just timeline) -> DAG
- Stack
	+ Greedy doesn't work
	+ 2D interpretation (L, R) -> magic dynamic programming
	+ Conjecture: Same theorem?

- Empty operation:
	+ Queue: Additional condition
	+ Stack: Simple extension

- Peak: 
	+ Queue: Greedy
		. "Push the left bounds" -> Creates an interval of length 0
	+ Stack: Greedy wouldn't work
		. Idea 1: Extend 2D DP solution
		. Idea 2: Change condition to 2D?