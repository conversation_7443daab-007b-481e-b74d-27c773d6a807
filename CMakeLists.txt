cmake_minimum_required(VERSION 3.14)
project(SeqCons CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Enable testing
enable_testing()

# Set up GTest
include(FetchContent)
FetchContent_Declare(
    googletest
    GIT_REPOSITORY https://github.com/google/googletest.git
    GIT_TAG v1.16.0
)
# For Windows: Prevent overriding the parent project's compiler/linker settings
set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
FetchContent_MakeAvailable(googletest)

# Enable warnings
if(MSVC)
    add_compile_options(/W4 /WX)
else()
    add_compile_options(-Wall -Wextra -Wpedantic -Werror)
endif()

# Automatically find all implementation files
file(GLOB_RECURSE IMPL_SOURCES
    "src/*.cpp"
)

# Add the library with all implementation files
add_library(seq_cons_lib ${IMPL_SOURCES})

target_include_directories(seq_cons_lib
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# Add main executable
add_executable(main src/main.cpp)
target_link_libraries(main PRIVATE seq_cons_lib)

# Add tests
add_executable(tests
    tests/stack_tests.cpp
    tests/queue_tests.cpp
    tests/deque_tests.cpp
)
target_link_libraries(tests
    PRIVATE
        GTest::gtest
        GTest::gtest_main
        seq_cons_lib
)

# Discover tests
include(GoogleTest)
gtest_discover_tests(tests)










