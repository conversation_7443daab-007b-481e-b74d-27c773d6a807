#include "common/printer.hpp"
#include "stack_cons/backtrack.hpp"
#include "stack_cons/generator.hpp"
#include "stack_cons/history_check.hpp"
#include "stack_cons/simple_dp.hpp"
#include "stack_cons/simple_top_down.hpp"
#include "stack_cons/testing.hpp"
#include <gtest/gtest.h>

using namespace common::printer;

class StackTest : public ::testing::Test {
protected:
  common::generator::InputConfig default_config = {
      .max_ops = 10, .variance_factor = 2, .permutation_factor = 0};
  common::URNG rng{42};
};

TEST_F(StackTest, BacktrackerSmallTest) {
  auto operations =
      stack_cons::generator::generateStackOps(default_config, rng);
  auto solver = stack_cons::Backtracker(operations);

  std::cout << "Generated Stack Operations:\n";
  printOperationsWithTimeline(operations);

  auto result = solver.constructHistory();

  if (result) {
    std::cout << "Solution found:\n";
    printResultWithTimeline(result, operations);
    EXPECT_TRUE(stack_cons::checkHistory(operations, result))
        << "Invalid solution found";
  } else {
    std::cout << "No solution found.\n";
    EXPECT_FALSE(true) << "Backtracker failed to find a solution";
  }
}

TEST_F(StackTest, BacktrackerCounterexample) {
  auto solver = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::Backtracker(ops).constructHistory();
  };

  auto counterexample = stack_cons::findCounterexample(
      solver, {.max_ops = 5, .variance_factor = 2, .permutation_factor = 0},
      1000);

  EXPECT_FALSE(counterexample.has_value()) << "Found unexpected counterexample";
}

TEST_F(StackTest, SimpleDPSmallTest) {
  auto operations =
      stack_cons::generator::generateStackOps(default_config, rng);
  auto solver = stack_cons::SimpleDP(operations);

  std::cout << "Generated Stack Operations:\n";
  printOperationsWithTimeline(operations);

  auto result = solver.constructHistory();

  if (result) {
    std::cout << "Solution found:\n";
    printResultWithTimeline(result, operations);
    EXPECT_TRUE(stack_cons::checkHistory(operations, result))
        << "Invalid solution found";
  } else {
    std::cout << "No solution found.\n";
    EXPECT_FALSE(true) << "SimpleDP failed to find a solution";
  }
}

TEST_F(StackTest, SimpleDPSmallCounterexample) {
  auto solver = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::SimpleDP(ops).constructHistory();
  };

  auto counterexample = stack_cons::findCounterexample(
      solver, {.max_ops = 10, .variance_factor = 2, .permutation_factor = 0},
      1000);

  EXPECT_FALSE(counterexample.has_value()) << "Found unexpected counterexample";
}

TEST_F(StackTest, SimpleDPCounterexample) {
  auto solver = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::SimpleDP(ops).constructHistory();
  };

  auto counterexample = stack_cons::findCounterexample(
      solver, {.max_ops = 100, .variance_factor = 2, .permutation_factor = 0},
      1000);

  EXPECT_FALSE(counterexample.has_value()) << "Found unexpected counterexample";
}

TEST_F(StackTest, SimpleTopDownSmallTest) {
  auto operations =
      stack_cons::generator::generateStackOps(default_config, rng);
  auto solver = stack_cons::SimpleTopDown(operations);

  std::cout << "Generated Stack Operations:\n";
  printOperationsWithTimeline(operations);

  auto result = solver.constructHistory();

  if (result) {
    std::cout << "Solution found:\n";
    printResultWithTimeline(result, operations);
    EXPECT_TRUE(stack_cons::checkHistory(operations, result))
        << "Invalid solution found";
  } else {
    std::cout << "No solution found.\n";
    EXPECT_FALSE(true) << "SimpleTopDown failed to find a solution";
  }
}

TEST_F(StackTest, SimpleTopDownSmallCounterexample) {
  auto solver = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::SimpleTopDown(ops).constructHistory();
  };

  auto counterexample = stack_cons::findCounterexample(
      solver, {.max_ops = 10, .variance_factor = 2, .permutation_factor = 0},
      1000);

  EXPECT_FALSE(counterexample.has_value()) << "Found unexpected counterexample";
}

TEST_F(StackTest, SimpleTopDownCounterexample) {
  auto solver = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::SimpleTopDown(ops).constructHistory();
  };

  auto counterexample = stack_cons::findCounterexample(
      solver, {.max_ops = 100, .variance_factor = 2, .permutation_factor = 0},
      1000);

  EXPECT_FALSE(counterexample.has_value()) << "Found unexpected counterexample";
}

TEST_F(StackTest, CompareSolvers) {
  auto solver1 = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::SimpleDP(ops).constructHistory();
  };
  auto solver2 = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::SimpleTopDown(ops).constructHistory();
  };

  auto comparison = stack_cons::compareSolvers(solver1, solver2);
  EXPECT_FALSE(comparison.has_value()) << "Found discrepancy between solvers";
}

TEST_F(StackTest, SimpleDPPerformanceTest) {
  auto solver = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::SimpleDP(ops).constructHistory();
  };

  auto slow_case = stack_cons::testSolverTime(solver, default_config);
  EXPECT_FALSE(slow_case.has_value()) << "Found unexpectedly slow case";
}

TEST_F(StackTest, SimpleTopDownPerformanceTest) {
  auto solver = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::SimpleTopDown(ops).constructHistory();
  };

  auto slow_case = stack_cons::testSolverTime(solver, default_config);
  EXPECT_FALSE(slow_case.has_value()) << "Found unexpectedly slow case";
}
