#include "common/printer.hpp"
#include "deque_cons/backtrack.hpp"
#include "deque_cons/generator.hpp"
#include "deque_cons/history_check.hpp"
#include "deque_cons/testing.hpp"
#include "deque_cons/printer.hpp"
#include <gtest/gtest.h>

using namespace common::printer;

class DequeTest : public ::testing::Test {
protected:
  common::generator::InputConfig default_config = {
      .max_ops = 10, .variance_factor = 2, .permutation_factor = 0};
  common::URNG rng{42};
};

TEST_F(DequeTest, BacktrackerBasicTest) {
  auto operations =
      deque_cons::generator::generateDequeOps(default_config, rng);

  std::cout << "Generated Deque Operations:\n";
  printOperationsWithTimeline(operations);

  auto solver = deque_cons::Backtracker(operations);

  auto result = solver.constructHistory();
  if (result) {
    EXPECT_TRUE(deque_cons::checkHistory(operations, result))
        << "Invalid solution found";
  }
}

TEST_F(DequeTest, BacktrackerCounterexampleTest) {
  auto solver = [](const std::vector<deque_cons::DequeOp>& ops) {
    return deque_cons::Backtracker(ops).constructHistory();
  };

  auto counterexample = deque_cons::findCounterexample(
      solver, {.max_ops = 10, .variance_factor = 2, .permutation_factor = 0},
      1000);

  EXPECT_FALSE(counterexample.has_value())
      << "Found unexpected counterexample for Backtracker";
}