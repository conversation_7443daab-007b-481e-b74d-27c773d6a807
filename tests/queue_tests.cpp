#include "common/printer.hpp"
#include "queue_cons/backtrack.hpp"
#include "queue_cons/generator.hpp"
#include "queue_cons/history_check.hpp"
#include "queue_cons/printer.hpp"
#include "queue_cons/simple_greedy.hpp"
#include "queue_cons/testing.hpp"
#include <gtest/gtest.h>

using namespace common::printer;

class QueueTest : public ::testing::Test {
protected:
  common::generator::InputConfig default_config = {
      .max_ops = 10, .variance_factor = 2, .permutation_factor = 0};
  common::URNG rng{42};
};

TEST_F(QueueTest, BacktrackerBasicTest) {
  auto operations =
      queue_cons::generator::generateQueueOps(default_config, rng);

  std::cout << "Generated Queue Operations:\n";
  printOperationsWithTimeline(operations);

  auto solver = queue_cons::Backtracker(operations);

  auto result = solver.constructHistory();
  if (result) {
    EXPECT_TRUE(queue_cons::checkHistory(operations, result))
        << "Invalid solution found";
  }
}

TEST_F(QueueTest, GeneratorConfigTest) {
  common::generator::InputConfig varied_config = {
      .max_ops = 20, .variance_factor = 5, .permutation_factor = 2};

  auto operations = queue_cons::generator::generateQueueOps(varied_config, rng);
  EXPECT_LE(operations.size(), varied_config.max_ops);
}

TEST_F(QueueTest, BacktrackerCounterexampleTest) {
  auto solver = [](const std::vector<queue_cons::QueueOp>& ops) {
    return queue_cons::Backtracker(ops).constructHistory();
  };

  auto counterexample = queue_cons::findCounterexample(
      solver, {.max_ops = 10, .variance_factor = 2, .permutation_factor = 0},
      1000);

  EXPECT_FALSE(counterexample.has_value())
      << "Found unexpected counterexample for Backtracker";
}

TEST_F(QueueTest, SimpleGreedyBasicTest) {
  auto operations =
      queue_cons::generator::generateQueueOps(default_config, rng);

  std::cout << "Generated Queue Operations:\n";
  printOperationsWithTimeline(operations);

  auto solver = queue_cons::SimpleGreedy(operations);

  auto result = solver.constructHistory();
  if (result) {
    std::cout << "Solution found:\n";
    printResultWithTimeline(result, operations);
    EXPECT_TRUE(queue_cons::checkHistory(operations, result))
        << "Invalid solution found";
  } else {
    std::cout << "No solution found.\n";
    EXPECT_FALSE(true) << "SimpleGreedy failed to find a solution";
  }
}

TEST_F(QueueTest, SimpleGreedySmallCounterexample) {
  auto solver = [](const std::vector<queue_cons::QueueOp>& ops) {
    return queue_cons::SimpleGreedy(ops).constructHistory();
  };

  auto counterexample = queue_cons::findCounterexample(
      solver, {.max_ops = 10, .variance_factor = 2, .permutation_factor = 0},
      1000);

  EXPECT_FALSE(counterexample.has_value())
      << "Found unexpected counterexample for SimpleGreedy";
}

TEST_F(QueueTest, SimpleGreedyLargeCounterexample) {
  auto solver = [](const std::vector<queue_cons::QueueOp>& ops) {
    return queue_cons::SimpleGreedy(ops).constructHistory();
  };

  auto counterexample = queue_cons::findCounterexample(
      solver, {.max_ops = 100, .variance_factor = 2, .permutation_factor = 0},
      1000);

  EXPECT_FALSE(counterexample.has_value())
      << "Found unexpected counterexample for SimpleGreedy";
}

TEST_F(QueueTest, SimpleGreedyPerformanceTest) {
  auto solver = [](const std::vector<queue_cons::QueueOp>& ops) {
    return queue_cons::SimpleGreedy(ops).constructHistory();
  };

  auto slow_case = queue_cons::testSolverTime(solver, default_config);
  EXPECT_FALSE(slow_case.has_value()) << "Found unexpectedly slow case";
}

TEST_F(QueueTest, CompareSimpleGreedyWithBacktracker) {
  auto solver1 = [](const std::vector<queue_cons::QueueOp>& ops) {
    return queue_cons::SimpleGreedy(ops).constructHistory();
  };
  auto solver2 = [](const std::vector<queue_cons::QueueOp>& ops) {
    return queue_cons::Backtracker(ops).constructHistory();
  };

  common::testing::TestConfigParamList config = {
      .trials_per_config = 100,
      .max_ops_values = {5, 7, 8, 10},
      .variance_factors = {1, 2, 5},
      .permutation_factors = {0, 1, 2}};
  auto comparison = queue_cons::compareSolvers(solver1, solver2, config);
  EXPECT_FALSE(comparison.has_value())
      << "Found discrepancy between SimpleGreedy and Backtracker";
}
