#pragma once

#include "cons.hpp"
#include "generator.hpp"
#include "sampling.hpp"
#include <algorithm>
#include <cassert>
#include <concepts>
#include <numeric>
#include <random>
#include <type_traits>
#include <utility>

namespace common::generator {

enum class ContainerEffect { ADD, REMOVE, PEEK, EMPTY };

template <typename C>
concept OpTypeClassifier = requires {
  requires std::is_enum_v<typename C::OpType>;
  { C::NUM_TYPES } -> std::convertible_to<int>;
  {
    C::getContainerEffect(std::declval<typename C::OpType>())
  } -> std::convertible_to<ContainerEffect>;
};

template <typename T>
concept Tracker = requires(T t) {
  {
    t.trackOperation(std::declval<typename T::OpType>())
  } -> std::convertible_to<std::pair<ElemId, OpId>>;
};

struct OpCount {
  int adds = 0;
  int removes = 0;
  int peeks = 0;
  int empty = 0;
  int container_size = 0;
};

[[nodiscard]] inline int getNumOperations(const CountConfig& config) {
  return 2 * config.num_elems + config.num_peeks + config.num_empty;
}

[[nodiscard]] DetailedConfig generateDetailedConfig(InputConfig config, URNG& rng);

[[nodiscard]] bool isOperationValid(ContainerEffect effect,
                                    const OpCount& count,
                                    const CountConfig& config);

void updateCounts(ContainerEffect effect, OpCount& count);

/**
 * @brief Samples operation types based on weights and constraints
 *
 * Ensures stack validity by tracking stack size and only allowing
 * pop/peek when stack is non-empty.
 */
template <OpTypeClassifier C>
std::vector<typename C::OpType>
generateOperationTypes(const CountConfig& config, URNG& rng) {
  std::discrete_distribution<> dist =
      common::sampling::discreteDirichlet(C::NUM_TYPES, rng);

  OpCount count;
  std::vector<typename C::OpType> sequence;
  auto num_ops = getNumOperations(config);
  sequence.reserve(num_ops);

  while (sequence.size() < static_cast<size_t>(num_ops)) {
    typename C::OpType op = static_cast<typename C::OpType>(dist(rng));
    auto effect = C::getContainerEffect(op);
    if (isOperationValid(effect, count, config)) {
      updateCounts(effect, count);
      sequence.push_back(op);
    }
  }
  return sequence;
}

template <Operation Op, OpTypeClassifier OpTypeClassifier, Tracker Tracker>
  requires std::same_as<typename Tracker::OpType,
                        typename OpTypeClassifier::OpType> &&
           std::same_as<decltype(Op::type), typename OpTypeClassifier::OpType>
[[nodiscard]] std::vector<Op> generateOps(const InputConfig& input_config,
                                          URNG& rng) {
  using OpType = typename OpTypeClassifier::OpType;

  DetailedConfig config = generateDetailedConfig(input_config, rng);
  if (!validateConfiguration(config)) {
    throw std::invalid_argument("Invalid generator configuration");
  }

  std::vector<OpType> operation_types =
      generateOperationTypes<OpTypeClassifier>(config.count, rng);

  std::vector<double> base_times =
      common::sampling::sampleBaseTimes(operation_types.size(), rng);

  common::sampling::permuteWithLimitedMovement(
      base_times, config.variance.max_element_distance,
      config.variance.max_displaced_elements, rng);

  std::vector<Op> operations;
  operations.reserve(operation_types.size());
  Tracker tracker;

  for (size_t i = 0; i < operation_types.size(); ++i) {
    auto [lower, upper] = common::sampling::sampleTimeWindow(
        base_times[i], config.variance.time_variance, rng);

    auto [element_id, op_id] = tracker.trackOperation(operation_types[i]);
    operations.emplace_back(op_id, operation_types[i], element_id,
                            Interval{lower, upper});
  }

  return operations;
}

} // namespace common::generator
