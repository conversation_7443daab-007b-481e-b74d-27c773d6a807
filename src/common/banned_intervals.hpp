#pragma once

#include "common/cons.hpp"
#include "common/interval.hpp"
#include <optional>
#include <vector>

namespace common {

/**
 * Manages a set of banned intervals by maintaining them in a non-overlapping,
 * sorted form. Provides efficient queries for finding valid points outside
 * of banned regions.
 *
 * Example:
 *   Input intervals: [1,3], [2,5], [8,9]
 *   Merged result:   [1,5], [8,9]
 */
class BannedIntervals {
private:
  // Non-overlapping, sorted intervals
  std::vector<Interval> intervals;

public:
  /**
   * Constructs merged set of non-overlapping intervals from input intervals.
   * Adjacent or overlapping intervals are merged.
   *
   * @param banned Vector of possibly overlapping intervals to be banned
   *
   * Time complexity: O(n log n) for initial sort
   * Space complexity: O(n) for stored intervals
   */
  explicit BannedIntervals(std::vector<Interval> banned);

  /**
   * Finds the leftmost valid point within the selection interval that doesn't
   * fall in any banned interval.
   *
   * @param selection Interval in which to find a valid point
   * @return The leftmost valid point if one exists, nullopt otherwise
   *
   * Time complexity: O(log n) where n is number of banned intervals
   * Space complexity: O(1)
   *
   * Example:
   *   Banned intervals: [1,5], [8,9]
   *   Selection: [3,7] -> returns 6
   *   Selection: [2,4] -> returns nullopt
   */
  std::optional<Time> findValidPoint(const Interval& selection) const;
};

} // namespace common
