#pragma once

#include <common/interval.hpp>
#include <algorithm>
#include <cassert>
#include <common/interval.hpp>
#include <list>
#include <optional>
#include <unordered_map>
#include <vector>

namespace common {

using OpId = int;
using ElemId = int;
using Time = int;
using Order = int;
using Rank = int;

constexpr OpId INVALID_OP_ID = -1;

template <typename T>
concept Operation = requires(T op) {
  { op.id } -> std::convertible_to<OpId>;
  { op.type };
  { op.element } -> std::convertible_to<ElemId>;
  { op.itv } -> std::convertible_to<Interval>;
};

struct TimeAndRank {
  Time time;
  Rank rank;
};

using TotalHistory = std::unordered_map<OpId, TimeAndRank>;

template <Operation O> class HistoryConstructor {
protected:
  std::vector<O> ops;

public:
  explicit HistoryConstructor(const std::vector<O>& ops) : ops(ops) {};

  virtual ~HistoryConstructor() = default;
  HistoryConstructor(const HistoryConstructor&) = delete;
  HistoryConstructor(HistoryConstructor&&) = delete;
  HistoryConstructor& operator=(const HistoryConstructor&) = delete;
  HistoryConstructor& operator=(HistoryConstructor&&) = delete;

  /**
   * Constructs a valid sequential history for the stack operations.
   *
   * @return Optional mapping from operation ID to (time, rank) pair where:
   *         - time: execution time of the operation (must be within bounds)
   *         - rank: position in execution sequence (1-based)
   *         Returns nullopt if no valid history exists.
   */
  virtual std::optional<std::list<OpId>> constructTotalOrder() = 0;

  virtual std::optional<TotalHistory> constructHistory() = 0;

  virtual bool isPartialHistoryValid() {
    return constructTotalOrder().has_value();
  }
};

/* Checks if an operation can be executed at time t. */
template <Operation O> inline bool canExec(const O& op, Time t) {
  return isIncluded(op.itv, t);
}

template <Operation O>
std::unordered_map<OpId, O> toMap(const std::vector<O>& ops) {
  std::unordered_map<OpId, O> map;
  for (const auto& op : ops) {
    map.emplace(op.id, op);
  }
  return map;
}

/**
 * Converts a total order of operation IDs to a history.
 * @return Mapping from operation ID to (time, rank) pair
 */
template <Operation O>
[[nodiscard]] TotalHistory convertOrderToHistory(const std::list<OpId>& order,
                                                 const std::vector<O>& ops) {
  auto op_map = toMap(ops);
  TotalHistory history{};
  Rank rank = 0;
  Time time = 0;
  for (OpId op_id : order) {
    time = std::max(time, op_map.at(op_id).lower());
    history[op_id] = {time, rank++};
  }
  return history;
}

/**
 * Extract the total order from a history.
 * @return List of operation IDs in the order they were executed
 */
[[nodiscard]] std::list<OpId> extractOrder(const TotalHistory& history) {
  std::vector<OpId> ops(history.size());
  for (const auto& [op_id, time_rank] : history) {
    ops[time_rank.rank] = op_id;
  }
  return std::list<OpId>(ops.begin(), ops.end());
}

} // namespace common
