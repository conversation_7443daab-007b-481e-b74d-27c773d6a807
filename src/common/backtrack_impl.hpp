#pragma once

#include "cons.hpp"
#include <algorithm>
#include <cassert>
#include <concepts>
#include <unordered_set>

namespace common::solver {

template <typename T>
concept Simulator = requires(T t) {
  requires Operation<typename T::Op>;
  {
    t.canAddOperation(std::declval<typename T::Op>())
  } -> std::convertible_to<bool>;
  { t.addOperation(std::declval<typename T::Op>()) };
  { t.removeOperation(std::declval<typename T::Op>()) };
  { t.clear() };
};

template <Simulator S>
class Backtracker : public HistoryConstructor<typename S::Op> {
public:
  template <std::ranges::input_range R>
    requires std::convertible_to<std::ranges::range_value_t<R>, typename S::Op>
  explicit Backtracker(R&& ops)
      : HistoryConstructor<typename S::Op>(std::forward<R>(ops)) {}

  std::optional<std::list<OpId>> constructTotalOrder() {
    auto history = constructHistory();
    if (!history) {
      return std::nullopt;
    }

    std::vector<std::pair<OpId, TimeAndRank>> sorted_ops;
    for (const auto& [op_id, time_rank] : *history) {
      sorted_ops.push_back({op_id, time_rank});
    }

    std::ranges::sort(sorted_ops, [](const auto& a, const auto& b) {
      return a.second.rank < b.second.rank;
    });

    std::list<OpId> order;
    for (const auto& [op_id, _] : sorted_ops) {
      order.push_back(op_id);
    }
    return order;
  }

  std::optional<TotalHistory> constructHistory() {
    if (status == Status::SOLUTION_FOUND) {
      return current_history;
    } else if (status == Status::NO_SOLUTION) {
      return std::nullopt;
    }
    assert(status == Status::NOT_SEARCHED);

    used_ops.clear();
    current_history.clear();
    simulator.clear();

    if (backtrackHistory(earliestTime())) {
      status = Status::SOLUTION_FOUND;
      return current_history;
    }

    status = Status::NO_SOLUTION;
    return std::nullopt;
  }

  inline bool isPartialHistoryValid() { return constructHistory().has_value(); }

private:
  using HistoryConstructor<typename S::Op>::ops;

  enum class Status { NOT_SEARCHED = -1, NO_SOLUTION = 0, SOLUTION_FOUND = 1 };
  Status status{Status::NOT_SEARCHED};

  std::unordered_set<ElemId> used_ops;
  TotalHistory current_history;
  S simulator;

  inline Time earliestTime() {
    return std::min_element(ops.cbegin(), ops.cend(),
                            [](const auto& a, const auto& b) {
                              return a.lower() < b.lower();
                            })
        ->lower();
  }

  bool backtrackHistory(Time last_time) {
    if (used_ops.size() == ops.size()) {
      return true;
    }

    for (const auto& op : ops) {
      if (used_ops.contains(op.id) || !simulator.canAddOperation(op)) {
        continue;
      }

      Time curr_time = std::max(last_time, op.lower());
      if (curr_time > op.upper()) {
        return false;
      }

      simulator.addOperation(op);
      used_ops.insert(op.id);
      current_history[op.id] = {curr_time,
                                static_cast<int>(current_history.size())};

      if (backtrackHistory(curr_time)) {
        return true;
      }

      simulator.removeOperation(op);
      used_ops.erase(op.id);
      current_history.erase(op.id);
    }

    return false;
  }
};

} // namespace common::solver
