#pragma once

#include <algorithm>
#include <random>
#include <set>
#include <vector>

namespace common {
using URNG = std::mt19937;
}

namespace common::sampling {

// Generates n samples from Dirichlet distribution with uniform concentration
std::vector<double> uniformDirichlet(int n, URNG& rng);

// Generates a discrete distribution from Dirichlet samples
std::discrete_distribution<> discreteDirichlet(int n, URNG& rng);

// Randomly partition n into k positive integers that sum to n
std::vector<int> randomPartition(int n, int k, URNG& rng);

/**
 * @brief Generates base timestamps for operations
 *
 * Samples from uniform distribution and sorts to ensure total order.
 */
std::vector<double> sampleBaseTimes(int operation_count, URNG& rng);

/**
 * @brief Generates time window [lower, upper] around base_time,
 *        ensuring base_time is always within bounds
 *
 * @param base_time Center point for the time window
 * @param variance Controls window size (standard deviation)
 * @return std::pair<int,int> Lower and upper bounds (inclusive)
 */
std::pair<int, int> sampleTimeWindow(int base_time, double variance,
                                     URNG& rng);

/**
 * @brief Permutes array elements with limited movement distance
 *
 * @param arr Array to permute
 * @param max_distance Maximum size of each shuffled segment
 * @param max_displaced Maximum total number of elements to move
 * @param rng Random number generator
 */
template <typename T>
void permuteWithLimitedMovement(std::vector<T>& arr, int max_distance,
                                int max_displaced, URNG& rng) {
  if (max_distance <= 0 || max_displaced <= 0)
    return;

  int n = arr.size();
  if (n < 2)
    return;

  // Generate segment sizes
  std::vector<int> segment_sizes;
  int total_size = 0;
  int num_segments = 0;
  std::uniform_int_distribution<int> size_dist(2, max_distance + 1);

  while (true) {
    int size = std::min(size_dist(rng), n - total_size);
    if (total_size + size + num_segments > n)
      break;
    if (total_size + size > max_displaced)
      break;

    segment_sizes.push_back(size);
    total_size += size;
    num_segments++;
  }

  if (segment_sizes.empty())
    return;

  // Partition remaining space
  int remaining = n - total_size;
  auto gaps = randomPartition(remaining + 2, num_segments + 1, rng);
  gaps.front()--; // First gap can be 0
  gaps.back()--;  // Last gap can be 0

  // Shuffle segments according to partition
  int pos = gaps[0];
  for (size_t i = 0; i < segment_sizes.size(); i++) {
    std::shuffle(arr.begin() + pos, arr.begin() + pos + segment_sizes[i], rng);
    pos += segment_sizes[i] + gaps[i + 1];
  }
}

} // namespace common::sampling
