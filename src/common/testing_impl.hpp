#pragma once

#include "cons.hpp"
#include "generator.hpp"
#include "history_check.hpp"
#include "printer.hpp"
#include "testing.hpp"
#include <algorithm>
#include <chrono>
#include <thread>
#include <type_traits>

namespace common::testing {

template <auto Func>
  requires std::is_invocable_v<decltype(Func), int, int, double, int>
class ParamCombinator {
public:
  using result = std::invoke_result_t<decltype(Func), int, int, double, int>;
  using function = decltype(Func);

  ParamCombinator(int trials_per_config, std::vector<int> max_ops_values,
                  std::vector<double> variance_factors,
                  std::vector<int> permutation_factors)
      : trials_(trials_per_config), max_ops_(std::move(max_ops_values)),
        variances_(std::move(variance_factors)),
        permutations_(std::move(permutation_factors)),
        total_(max_ops_.size() * variances_.size() * permutations_.size() *
               trials_) {}

  ParamCombinator(TestConfigParamList params)
      : ParamCombinator(params.trials_per_config,
                        std::move(params.max_ops_values),
                        std::move(params.variance_factors),
                        std::move(params.permutation_factors)) {}

  void next() {
    ++count_;

    if (static_cast<int>(trial_) + 1 < trials_) {
      ++trial_;
      return;
    }

    trial_ = 0;
    if (perm_idx_ + 1 < permutations_.size()) {
      ++perm_idx_;
      return;
    }

    perm_idx_ = 0;
    if (var_idx_ + 1 < variances_.size()) {
      ++var_idx_;
      return;
    }

    var_idx_ = 0;
    if (ops_idx_ + 1 < max_ops_.size()) {
      ++ops_idx_;
      return;
    }
  }

  bool isDone() const { return count_ == total_; }

  std::tuple<int, int, double, int> params() const {
    return std::make_tuple(static_cast<int>(trial_), max_ops_[ops_idx_],
                           variances_[var_idx_], permutations_[perm_idx_]);
  }

  auto current() const {
    auto [trial, max_ops, variance, permutation] = params();
    return Func(trial, max_ops, variance, permutation);
  }

  size_t count() const { return count_; }

  size_t total() const { return total_; }

private:
  const int trials_;
  const std::vector<int> max_ops_;
  const std::vector<double> variances_;
  const std::vector<int> permutations_;

  size_t ops_idx_{0};
  size_t var_idx_{0};
  size_t perm_idx_{0};
  size_t trial_{0};
  size_t count_{0};
  size_t total_;
};

template <Operation Op, class TestGenerator>
  requires std::is_same_v<typename TestGenerator::result, std::vector<Op>>
std::optional<std::vector<Op>>
findCounterexample(const Solver<Op>& solver,
                   const common::generator::InputConfig& config,
                   int num_trials) {
  TestGenerator generator(num_trials, {config.max_ops},
                          {config.variance_factor},
                          {config.permutation_factor});

  for (; !generator.isDone(); generator.next()) {
    printer::updateProgressBar(generator.count(), num_trials);
    auto operations = generator.current();

    try {
      auto result = solver(operations);
      if (!result || !checkHistory(operations, result)) {
        printer::out_stream << "\rFound counterexample at trial "
                            << generator.count() + 1 << "/" << num_trials
                            << "\n";
        printer::printOperationsWithTimeline(operations);
        return operations;
      }
    } catch (const std::exception& e) {
      printer::out_stream << "\rFound counterexample (exception) at trial "
                          << generator.count() + 1 << "/" << num_trials << "\n";
      printer::printOperationsWithTimeline(operations);
      return operations;
    }
  }

  printer::out_stream << "\rTesting: [" << std::string(50, '=') << "] 100%"
                      << "\n";
  printer::out_stream << "No counterexample found after " << num_trials
                      << " trials                        " << "\n";
  return std::nullopt;
}

template <Operation Op, class TestGenerator>
  requires std::is_same_v<typename TestGenerator::result, std::vector<Op>>
void benchmarkConfigurations(const Solver<Op>& solver,
                             const TestConfigParamList& param_list) {
  TestGenerator generator(param_list);
  const int trials_per_config = param_list.trials_per_config;
  const int num_permutations = param_list.permutation_factors.size();

  printer::out_stream << "\nBenchmarking configurations:\n";
  printer::out_stream << std::setw(10) << "Max Ops" << std::setw(15)
                      << "Variance" << std::setw(15) << "Permutation"
                      << std::setw(15) << "Success Rate" << std::setw(15)
                      << "Avg Time (ms)" << "\n";

  int successes = 0;
  auto start_time = std::chrono::high_resolution_clock::now();

  for (; !generator.isDone(); generator.next()) {
    if (generator.count() % (trials_per_config * num_permutations) == 0) {
      printer::out_stream << std::string(70, '-') << "\n";
    }

    auto operations = generator.current();
    auto result = solver(operations);
    if (result) {
      successes++;
    }

    if ((generator.count() + 1) % trials_per_config == 0) {
      auto [_, max_ops, variance, permutation] = generator.params();
      auto end_time = std::chrono::high_resolution_clock::now();
      auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
          end_time - start_time);
      double success_rate = (100.0 * successes) / trials_per_config;
      double avg_time =
          static_cast<double>(duration.count()) / trials_per_config;

      printer::out_stream << std::fixed << std::setprecision(2) << std::setw(10)
                          << max_ops << std::setw(15) << variance
                          << std::setw(15) << permutation << std::setw(15)
                          << success_rate << "%" << std::setw(15) << avg_time
                          << "\n";

      successes = 0;
      start_time = std::chrono::high_resolution_clock::now();
    }
  }
}

template <Operation Op, class TestGenerator>
  requires std::is_same_v<typename TestGenerator::result, std::vector<Op>>
std::optional<std::vector<Op>>
testSolverTime(const Solver<Op>& solver,
               const common::generator::InputConfig& config,
               std::chrono::milliseconds time_threshold, int num_trials) {
  TestGenerator generator(num_trials, {config.max_ops},
                          {config.variance_factor},
                          {config.permutation_factor});

  for (; !generator.isDone(); generator.next()) {
    printer::updateProgressBar(generator.count(), num_trials);
    auto operations = generator.current();
    bool finished = false;
    std::optional<TotalHistory> result;

    std::thread solver_thread([&]() {
      result = solver(operations);
      finished = true;
    });

    auto start = std::chrono::high_resolution_clock::now();

    while (std::chrono::duration_cast<std::chrono::milliseconds>(
               std::chrono::high_resolution_clock::now() - start) <
           time_threshold) {
      if (finished)
        break;
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - start);

    if (!finished) {
      printer::out_stream << "Found slow test case at trial "
                          << generator.count() << ":\n"
                          << "  Runtime: " << duration.count() << "ms\n"
                          << "  Status: Hanging\n"
                          << "  Config: " << config << "\n";

      solver_thread.detach();
      return operations;
    }

    solver_thread.join();

    if (duration > time_threshold) {
      printer::out_stream << "Found slow test case at trial "
                          << generator.count() << ":\n"
                          << "  Runtime: " << duration.count() << "ms\n"
                          << "  Status: Completed\n"
                          << "  Config: " << config << "\n";
      return operations;
    }
  }

  printer::out_stream << "\rTesting: [" << std::string(50, '=') << "] 100%"
                      << "\n";
  printer::out_stream << "No slow test cases found after " << num_trials
                      << " trials\n";
  return std::nullopt;
}

template <Operation Op, class TestGenerator>
  requires std::is_same_v<typename TestGenerator::result, std::vector<Op>>
std::optional<
    std::tuple<generator::InputConfig, std::vector<Op>,
               std::optional<TotalHistory>, std::optional<TotalHistory>>>
compareSolvers(const Solver<Op>& solver1, const Solver<Op>& solver2,
               const common::testing::TestConfigParamList& config) {
  TestGenerator generator(config);

  size_t total_tests = generator.total();
  size_t current_test = 0;

  printer::out_stream << "Comparing solvers...\n";

  for (; !generator.isDone(); generator.next()) {
    // Update progress
    current_test++;
    if (current_test % 100 == 0 || current_test == total_tests) {
      float progress = static_cast<float>(current_test) / total_tests;
      const int bar_width = 50;
      int pos = static_cast<int>(bar_width * progress);

      printer::out_stream << "\rProgress: [";
      for (int i = 0; i < bar_width; ++i) {
        if (i < pos)
          printer::out_stream << "=";
        else if (i == pos)
          printer::out_stream << ">";
        else
          printer::out_stream << " ";
      }

      auto [trial, max_ops, variance, permutation] = generator.params();
      printer::out_stream << "] " << std::fixed << std::setprecision(1)
                          << (progress * 100.0) << "% "
                          << "(" << current_test << "/" << total_tests << ") "
                          << "Max Ops: " << max_ops << ", Var: " << variance
                          << ", Perm: " << permutation << std::flush;
    }

    auto operations = generator.current();
    auto [trial, max_ops, variance, permutation] = generator.params();
    auto config =
        common::generator::InputConfig{.max_ops = max_ops,
                                       .variance_factor = variance,
                                       .permutation_factor = permutation};

    auto result1 = solver1(operations);
    auto result2 = solver2(operations);

    if (result1 && !checkHistory(operations, result1)) {
      printer::out_stream << "\n\nFound invalid result from solver1:\n";
      printer::out_stream << "Config: " << config << ", Trial: " << trial
                          << "\n\n";
      printer::out_stream << "Operations:\n";
      common::printer::printOperationsWithTimeline(operations);
      printer::out_stream << "\nSolver1 result: invalid\n";

      if (result1) {
        printer::out_stream << "\nSolver1 solution:\n";
        common::printer::printResultWithTimeline(result1, operations);
      }

      return std::make_tuple(config, operations, result1, result2);
    }

    if (result2 && !checkHistory(operations, result2)) {
      printer::out_stream << "\n\nFound invalid result from solver2:\n";
      printer::out_stream << "Config: " << config << ", Trial: " << trial
                          << "\n\n";
      printer::out_stream << "Operations:\n";
      common::printer::printOperationsWithTimeline(operations);
      printer::out_stream << "\nSolver2 result: invalid\n";

      if (result2) {
        printer::out_stream << "\nSolver2 solution:\n";
        common::printer::printResultWithTimeline(result2, operations);
      }

      return std::make_tuple(config, operations, result1, result2);
    }

    bool valid1 = result1 && true;
    bool valid2 = result2 && true;

    if (valid1 != valid2) {
      printer::out_stream << "\n\nFound difference:\n";
      printer::out_stream << "Config: " << config << ", Trial: " << trial
                          << "\n\n";
      printer::out_stream << "Operations:\n";
      common::printer::printOperationsWithTimeline(operations);
      printer::out_stream << "\nSolver1 result: "
                          << (valid1 ? "valid" : "invalid");
      printer::out_stream << "\nSolver2 result: "
                          << (valid2 ? "valid" : "invalid") << "\n";

      if (valid1 && result1) {
        printer::out_stream << "\nSolver1 solution:\n";
        common::printer::printResultWithTimeline(result1, operations);
      }
      if (valid2 && result2) {
        printer::out_stream << "\nSolver2 solution:\n";
        common::printer::printResultWithTimeline(result2, operations);
      }

      return std::make_tuple(config, operations, result1, result2);
    }
  }

  printer::out_stream
      << "\nNo differences found after testing all configurations.\n";
  return std::nullopt;
}

} // namespace common::testing
