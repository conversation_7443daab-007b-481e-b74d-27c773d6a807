#pragma once

#include <ostream>
#include <random>

namespace common::generator {

/**
 * @brief Operation count configuration
 */
struct CountConfig {
  int num_elems; ///< Number of push/pop/enqueue/dequeue operations
  int num_peeks; ///< Number of peek operations
  int num_empty; ///< Number of empty check operations
};

/**
 * @brief Time variance configuration
 */
struct VarianceConfig {
  /// Controls the size of time windows. Larger values create wider intervals
  double time_variance{2.0};
  /// Maximum distance between elements in a single operation
  int max_element_distance{0};
  /// Maximum total number of elements to move during permutation
  int max_displaced_elements{0};
};

/**
 * @brief Configuration parameters for the generator
 */
struct DetailedConfig {
  CountConfig count;
  VarianceConfig variance;
};

struct InputConfig {
  int max_ops;
  double variance_factor;
  int permutation_factor;
};

inline std::ostream& operator<<(std::ostream& os, const CountConfig& config) {
  return os << "{.num_elems = " << config.num_elems
            << ", .num_peeks = " << config.num_peeks
            << ", .num_empty = " << config.num_empty << "}";
}

inline std::ostream& operator<<(std::ostream& os,
                                const VarianceConfig& config) {
  return os << "{.time_variance = " << config.time_variance
            << ", .max_element_distance = " << config.max_element_distance
            << ", .max_displaced_elements = " << config.max_displaced_elements
          << "}";
}

inline std::ostream& operator<<(std::ostream& os,
                                const DetailedConfig& config) {
  return os << "{.count = " << config.count
            << ", .variance = " << config.variance << "}";
}

inline std::ostream& operator<<(std::ostream& os, const InputConfig& config) {
  return os << "{.max_ops = " << config.max_ops
            << ", .variance_factor = " << config.variance_factor
            << ", .permutation_factor = " << config.permutation_factor << "}";
}

inline bool validateConfiguration(const DetailedConfig& config) {
  return (config.count.num_elems > 0 && config.count.num_peeks >= 0 &&
          config.variance.time_variance > 0);
}

} // namespace common::generator
