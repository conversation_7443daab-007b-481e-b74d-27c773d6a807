#pragma once

#include <functional>
#include <ostream>
#include <utility>

namespace common {

struct Interval {
  using T = int;
  T l;
  T r;

  inline bool operator==(const Interval& other) const {
    return l == other.l && r == other.r;
  }
};

inline bool isValid(const Interval& itv) { return itv.l <= itv.r; }

inline bool isIncluded(const Interval& itv, Interval::T t) {
  return itv.l <= t && t <= itv.r;
}

inline bool isStrictlyIn(const Interval& outer, const Interval& inner) {
  return outer.l < inner.l && inner.r < outer.r;
}

inline void raiseLowerBound(Interval& target, const Interval& reference) {
  target.l = std::max(target.l, reference.l);
}

inline void reduceUpperBound(Interval& target, const Interval& reference) {
  target.r = std::min(target.r, reference.r);
}

inline std::ostream& operator<<(std::ostream& os, const Interval& itv) {
  return os << "[" << itv.l << ", " << itv.r << "]";
}
} // namespace common


template <> struct std::hash<common::Interval> {
  inline std::size_t operator()(const common::Interval& itv) const {
    return std::hash<common::Interval::T>()(itv.l) ^ std::hash<common::Interval::T>()(itv.r);
  }
};