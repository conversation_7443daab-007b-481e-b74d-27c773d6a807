#pragma once

#include "banned_intervals.hpp"
#include "cons.hpp"
#include <algorithm>
#include <iostream>
#include <map>
#include <optional>
#include <vector>

namespace common::filter_empty {

template <typename T>
concept Defs = requires {
  requires Operation<typename T::Op>;
  requires std::is_same_v<typename T::ElemOpsMap,
                          std::unordered_map<ElemId, typename T::ElemOps>>;
  {
    std::declval<typename T::Solver>().constructTotalOrder(
        std::declval<typename T::ElemOpsMap>())
  } -> std::convertible_to<std::optional<std::list<OpId>>>;
  { T::isEmptyOp(std::declval<typename T::Op>()) };
  {
    T::filterOpsByElem(std::declval<std::vector<typename T::Op>>())
  } -> std::convertible_to<typename T::ElemOpsMap>;
  {
    T::getInItv(std::declval<typename T::ElemOps>())
  } -> std::convertible_to<Interval>;
};

namespace {
template <Defs T>
std::pair<std::vector<typename T::Op>, std::vector<typename T::Op>>
filterEmptyOps(const std::vector<typename T::Op>& ops) {
  std::vector<typename T::Op> non_empty_ops;
  std::vector<typename T::Op> empty_ops;
  for (auto& op : ops) {
    if (T::isEmptyOp(op)) {
      empty_ops.push_back(op);
    } else {
      non_empty_ops.push_back(op);
    }
  }
  return {non_empty_ops, empty_ops};
}

template <Defs T>
BannedIntervals getBannedItvs(const typename T::ElemOpsMap& elem_ops) {
  std::vector<Interval> banned_itvs;
  for (const auto& [_, elem_op] : elem_ops) {
    banned_itvs.push_back(T::getInItv(elem_op));
  }
  return BannedIntervals(banned_itvs);
}

template <Defs T> Time minimumTime(const typename T::ElemOpsMap& elem_ops) {
  auto getBound = [](const auto& elem_op) { return T::getInItv(elem_op).l; };
  return getBound(std::min_element(elem_ops.cbegin(), elem_ops.cend(),
                                    [getBound](const auto& a, const auto& b) {
                                      return getBound(a.second) <
                                             getBound(b.second);
                                    })->second) - 1;
}

} // namespace

template <Defs T> class FilterEmpty {
public:
  std::optional<std::list<OpId>>
  constructTotalOrder(const std::vector<typename T::Op>& ops) {
    auto [non_empty_ops, empty_ops] = filterEmptyOps<T>(ops);

    typename T::ElemOpsMap elem_ops = T::filterOpsByElem(non_empty_ops);
    BannedIntervals banned_itvs = getBannedItvs<T>(elem_ops);

    partition = {{minimumTime<T>(elem_ops), {}}};

    if (!this->assignEmptyOps(empty_ops, banned_itvs)) {
      return std::nullopt;
    }

    this->partitionElemOps(elem_ops);

    return this->consFromPartition();
  }

private:
  struct EmptyIdsAndElemsOps {
    std::vector<OpId> empty_ids;
    typename T::ElemOpsMap elem_ops;
  };

  /*
   * Maps time to:
   * - empty_ids: IDs of empty operations executed at that time
   * - elem_ops: element operations to occur in the interval right after the
   * time
   */
  std::map<Time, EmptyIdsAndElemsOps> partition;

  /* Assigns empty operations to intervals. */
  bool assignEmptyOps(const std::vector<typename T::Op>& empty_ops,
                      const BannedIntervals& banned_itvs) {
    for (const auto& op : empty_ops) {
      auto time = banned_itvs.findValidPoint(op.itv);
      if (!time) {
        return false;
      }
      partition[time.value()].empty_ids.push_back(op.id);
    }
    return true;
  }

  /* Partitions element operations into intervals. */
  void partitionElemOps(const typename T::ElemOpsMap& elem_ops) {
    for (const auto& [elem_id, elem_op] : elem_ops) {
      auto time = T::getInItv(elem_op).l;
      prev(partition.lower_bound(time))
          ->second.elem_ops.emplace(elem_id, elem_op);
    }
  }

  std::optional<std::list<OpId>> consFromPartition() {
    std::list<OpId> order;
    for (const auto& [_, empty_ids_and_elem_ops] : partition) {
      for (const auto& empty_id : empty_ids_and_elem_ops.empty_ids) {
        order.push_back(empty_id);
      }

      auto elem_order = typename T::Solver{}.constructTotalOrder(
          empty_ids_and_elem_ops.elem_ops);

      if (!elem_order) {
        return std::nullopt;
      }

      order.splice(order.end(), elem_order.value());
    }

    return order;
  }
};

} // namespace common::filter_empty
