#include "generator_impl.hpp"

namespace common::generator {

[[nodiscard]] CountConfig generateCountConfig(int max_ops, URNG& rng) {
  // First determine num_elems (each element needs 2 ops)
  std::uniform_int_distribution<int> elems_dist(1, max_ops / 2);
  int num_elems = elems_dist(rng);

  // Then determine num_peeks and empty checks with remaining space
  int remaining_ops = max_ops - (2 * num_elems);
  std::uniform_int_distribution<int> ops_dist(0, remaining_ops);
  int num_peeks = ops_dist(rng);

  // Use some of the remaining space for empty checks
  remaining_ops -= num_peeks;
  std::uniform_int_distribution<int> empty_dist(0, remaining_ops);
  int num_empty = empty_dist(rng);

  return CountConfig{
      .num_elems = num_elems, .num_peeks = num_peeks, .num_empty = num_empty};
}

[[nodiscard]] DetailedConfig generateDetailedConfig(InputConfig config,
                                                    URNG& rng) {
  auto count = generateCountConfig(config.max_ops, rng);
  return DetailedConfig{
      .count = count,
      .variance = {.time_variance = config.variance_factor,
                   .max_element_distance = static_cast<int>(
                       config.permutation_factor * count.num_elems),
                   .max_displaced_elements = static_cast<int>(
                       config.permutation_factor * count.num_elems * 2)}};
}

[[nodiscard]] bool isOperationValid(ContainerEffect effect,
                                    const OpCount& count,
                                    const CountConfig& config) {
  // Calculate remaining operations needed
  int remaining_adds = config.num_elems - count.adds;
  int remaining_removes = config.num_elems - count.removes;
  int remaining_peeks = config.num_peeks - count.peeks;
  int remaining_empty = config.num_empty - count.empty;

  switch (effect) {
  case ContainerEffect::ADD:
    // Can add if we haven't reached element limit
    return remaining_adds > 0;

  case ContainerEffect::REMOVE:
    // Can remove if:
    // 1. Container is not empty
    // 2. We have remaining removes available
    // 3. If we have peeks remaining, we must keep at least one more remove
    // available
    return count.container_size > 0 && remaining_removes > 0 &&
           (remaining_peeks == 0 || remaining_removes > 1);

  case ContainerEffect::PEEK:
    // Can peek if:
    // 1. Container is not empty
    // 2. We haven't reached peek limit
    return count.container_size > 0 && remaining_peeks > 0;

  case ContainerEffect::EMPTY:
    // Can check empty if:
    // 1. Container is empty
    // 2. We haven't reached empty check limit
    return count.container_size == 0 && remaining_empty > 0;
  }
  return false;
}

void updateCounts(ContainerEffect effect, OpCount& count) {
  switch (effect) {
  case ContainerEffect::ADD:
    count.adds++;
    count.container_size++;
    break;
  case ContainerEffect::REMOVE:
    count.removes++;
    count.container_size--;
    break;
  case ContainerEffect::PEEK:
    count.peeks++;
    break;
  case ContainerEffect::EMPTY:
    count.empty++;
    break;
  }
}

} // namespace common::generator
