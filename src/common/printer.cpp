#include "printer.hpp"
#include <iomanip>

namespace common::printer {

std::ostream& out_stream = std::cout;

void updateProgressBar(int trial, int num_trials) {
  if (trial % (num_trials / 100 + 1) != 0 && trial != num_trials) {
    return;
  }

  const int bar_width = 50;
  float progress = static_cast<float>(trial) / num_trials;
  int pos = static_cast<int>(bar_width * progress);

  out_stream << "\rTesting: [";
  for (int i = 0; i < bar_width; ++i) {
    if (i < pos)
      out_stream << "=";
    else if (i == pos)
      out_stream << ">";
    else
      out_stream << " ";
  }
  out_stream << "] " << int(progress * 100.0) << "%" << std::flush;
}

} // namespace common::printer
