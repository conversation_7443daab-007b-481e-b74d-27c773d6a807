#pragma once

#include "cons.hpp"
#include <functional>
#include <initializer_list>
#include <tuple>
#include <type_traits>
#include <vector>

namespace common::testing {

struct TestConfigParamList {
  int trials_per_config;
  std::vector<int> max_ops_values;
  std::vector<double> variance_factors;
  std::vector<int> permutation_factors;
};

template <Operation O>
using Solver = std::function<std::optional<TotalHistory>(const std::vector<O>&)>;

} // namespace common::testing
