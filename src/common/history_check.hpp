#pragma once

#include "cons.hpp"
#include <algorithm>
#include <vector>

namespace common {

inline bool areRanksUnique(const TotalHistory& history) {
  std::vector<int> ranks;
  ranks.reserve(history.size());
  for (const auto& [_, time_rank] : history) {
    ranks.push_back(time_rank.rank);
  }
  std::sort(ranks.begin(), ranks.end());
  return std::adjacent_find(ranks.cbegin(), ranks.cend()) == ranks.cend();
}

template <Operation O>
bool areTimesWithinBounds(const std::vector<O>& ops,
                          const TotalHistory& history) {
  for (const auto& op : ops) {
    const auto& time_rank = history.at(op.id);
    if (!(op.lower() <= time_rank.time && time_rank.time <= op.upper())) {
      return false;
    }
  }
  return true;
}

template <Operation O> bool isOrderValid(const std::vector<O>& ordered_ops);

/**
 * Validates a constructed history for a sequence of stack operations.
 * 
 * @param ops The original sequence of stack operations
 * @param result The constructed history mapping operations to time and rank
 * @return true if the history is valid, false otherwise
 */
template <Operation O>
bool checkHistory(const std::vector<O>& ops,
                  const std::optional<TotalHistory>& result) {
  if (!result || result->size() != ops.size()) {
    return false;
  }

  if (!areRanksUnique(*result)) {
    return false;
  }

  if (!areTimesWithinBounds(ops, *result)) {
    return false;
  }

  // Create ordered sequence of operations
  std::vector<O> ordered_ops = ops;
  std::sort(ordered_ops.begin(), ordered_ops.end(),
            [&result](const O& a, const O& b) {
              return result->at(a.id).rank < result->at(b.id).rank;
            });

  return isOrderValid(ordered_ops);
}
    
} // namespace common
