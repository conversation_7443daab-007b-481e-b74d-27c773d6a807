#include "sampling.hpp"

namespace common::sampling {

std::vector<double> uniformDirichlet(int n, URNG& rng) {
                
  std::gamma_distribution<> dist(1.0, 1.0);
  std::vector<double> samples(n);
  for (int i = 0; i < n; ++i) {
    samples[i] = dist(rng);
  }
  double sum = std::accumulate(samples.begin(), samples.end(), 0.0);
  for (double& sample : samples) {
    sample /= sum;
  }
  return samples;
}

std::discrete_distribution<> discreteDirichlet(int n, URNG& rng) {
  auto samples = uniformDirichlet(n, rng);
  return std::discrete_distribution<>(samples.begin(), samples.end());
}

// Randomly partition n into k positive integers that sum to n
std::vector<int> randomPartition(int n, int k, URNG& rng) {
  if (k == 1)
    return {n};

  // Generate k-1 random points in range [1, n-1]
  std::set<int> point_set;
  std::uniform_int_distribution<int> dist(1, n - 1);
  while (static_cast<int>(point_set.size()) < k - 1) {
    point_set.insert(dist(rng));
  }
  std::vector<int> points(point_set.cbegin(), point_set.cend());

  // Convert points to partition sizes
  std::vector<int> partition(k);
  partition[0] = points[0];
  for (int i = 1; i < k - 1; i++) {
    partition[i] = points[i] - points[i - 1];
  }
  partition[k - 1] = n - points[k - 2];

  return partition;
}

std::vector<double> sampleBaseTimes(int operation_count, URNG& rng) {
  std::uniform_real_distribution<> dist(0, operation_count);

  std::vector<double> times(operation_count);
  for (int i = 0; i < operation_count; ++i) {
    times[i] = dist(rng);
  }

  std::sort(times.begin(), times.end());
  return times;
}

std::pair<int, int> sampleTimeWindow(int base_time, double variance, URNG& rng) {
  std::normal_distribution<> dist(base_time, std::sqrt(variance));

  // Ensure base_time falls within bounds (reflect around base_time)
  double lower_d = dist(rng);
  double upper_d = dist(rng);

  if (lower_d > base_time) {
    // Reflect lower bound around base_time
    lower_d = base_time + (base_time - lower_d);
  }
  if (upper_d < base_time) {
    // Reflect upper bound around base_time
    upper_d = base_time + (base_time - upper_d);
  }

  // Ensure bounds are non-negative integers
  int lower = std::max(0, static_cast<int>(std::round(lower_d)));
  int upper = std::max(lower, static_cast<int>(std::round(upper_d)));

  return {lower, upper};
}

} // namespace common::sampling
