#include "common/banned_intervals.hpp"

namespace common {

BannedIntervals::BannedIntervals(std::vector<Interval> banned) {
  if (banned.empty())
    return;

  // Remove invalid intervals
  banned.erase(
      std::remove_if(banned.begin(), banned.end(),
                     [](const Interval& itv) { return !isValid(itv); }),
      banned.end());

  // Sort by left endpoint
  std::sort(banned.begin(), banned.end(),
            [](const Interval& a, const Interval& b) { return a.l < b.l; });

  // Merge overlapping intervals
  intervals.push_back(banned[0]);
  for (size_t i = 1; i < banned.size(); i++) {
    if (banned[i].l <= intervals.back().r + 1) {
      // Merge if intervals touch or overlap
      intervals.back().r = std::max(intervals.back().r, banned[i].r);
    } else {
      intervals.push_back(banned[i]);
    }
  }
}

std::optional<Time>
BannedIntervals::findValidPoint(const Interval& selection) const {
  if (intervals.empty()) {
    return selection.l;
  }

  // Binary search for first interval that could affect selection.l
  auto it = std::lower_bound(
      intervals.cbegin(), intervals.cend(), Interval{selection.l, selection.l},
      [](const Interval& a, const Interval& b) { return a.r < b.l; });

  if (it == intervals.end()) {
    // All banned intervals are before selection
    return selection.l;
  }

  if (it->l > selection.l) {
    // Gap found before first intersecting banned interval
    return selection.l;
  }

  // First valid point would be after this banned interval
  Time candidate = it->r + 1;
  return candidate <= selection.r ? std::optional<Time>(candidate)
                                  : std::nullopt;
}

} // namespace common
