#pragma once

#include "cons.hpp"
#include <iomanip>
#include <iostream>
#include <optional>
#include <vector>

namespace common::printer {

extern std::ostream& out_stream;

inline constexpr int TIMELINE_WIDTH = 50;
inline constexpr int SCALE_INTERVAL = 5;

template <typename T>
concept OperationWithPrintableType = Operation<T> && requires(T op) {
  { toString(op.type) } -> std::convertible_to<std::string>;
};

template <OperationWithPrintableType O>
void printOperations(const std::vector<O>& operations) {
  auto sorted_ops = operations;

  std::sort(sorted_ops.begin(), sorted_ops.end(), [](const O& a, const O& b) {
    if (a.element != b.element) {
      return a.element < b.element;
    }
    return static_cast<int>(a.type) < static_cast<int>(b.type);
  });

  for (const auto& op : sorted_ops) {
    out_stream << "ID:" << std::setw(2) << op.id << ", Type:" << std::setw(14)
              << toString(op.type) << ", Element:" << std::setw(2) << op.element
              << ", Lower:" << std::setw(2) << op.lower()
              << ", Upper:" << std::setw(2) << op.upper() << "\n";
  }
}

template <OperationWithPrintableType O>
void printOperationsWithTimeline(const std::vector<O>& operations) {
  int max_upper = 0;

  auto sorted_ops = operations;
  std::sort(sorted_ops.begin(), sorted_ops.end(), [](const O& a, const O& b) {
    if (a.element != b.element) {
      return a.element < b.element;
    }
    return static_cast<int>(a.type) < static_cast<int>(b.type);
  });

  for (const auto& op : sorted_ops) {
    max_upper = std::max(max_upper, op.upper());
  }

  for (const auto& op : sorted_ops) {
    out_stream << "ID:" << std::setw(2) << op.id << " " << std::setw(14)
              << toString(op.type) << " Elem:" << std::setw(2) << op.element
              << " [" << std::setw(2) << op.lower() << "," << std::setw(2)
              << op.upper() << "] ";

    int lower_pos = (op.lower() * TIMELINE_WIDTH) / max_upper;
    int upper_pos = (op.upper() * TIMELINE_WIDTH) / max_upper;

    for (int i = 0; i <= TIMELINE_WIDTH; i++) {
      if (i < lower_pos) {
        out_stream << " ";
      } else if (i > upper_pos) {
        out_stream << " ";
      } else {
        out_stream << "─";
      }
    }
    out_stream << "\n";
  }

  out_stream << std::string(20, ' ');

  for (int i = 0; i <= TIMELINE_WIDTH; i += SCALE_INTERVAL) {
    int time_value = (i * max_upper) / TIMELINE_WIDTH;
    out_stream << std::setw(SCALE_INTERVAL) << time_value;
  }
  out_stream << "\n";
}

template <OperationWithPrintableType O>
void printResultWithTimeline(const std::optional<TotalHistory>& result,
                             const std::vector<O>& operations) {

  if (!result) {
    out_stream << "No valid history found.\n";
    return;
  }

  int max_time = 0;

  std::vector<std::pair<int, TimeAndRank>> sorted_result;
  for (const auto& [op_id, time_rank] : *result) {
    sorted_result.push_back({op_id, time_rank});
    max_time = std::max(max_time, time_rank.time);
  }

  for (const auto& op : operations) {
    max_time = std::max(max_time, op.upper());
  }

  std::sort(sorted_result.begin(), sorted_result.end(),
            [&operations](const auto& a, const auto& b) {
              const O& op_a = operations[a.first];
              const O& op_b = operations[b.first];
              if (op_a.element != op_b.element) {
                return op_a.element < op_b.element;
              }
              return static_cast<int>(op_a.type) < static_cast<int>(op_b.type);
            });

  for (const auto& [op_id, time_rank] : sorted_result) {
    const auto& op = operations[op_id];
    out_stream << "ID:" << std::setw(2) << op_id << " " << std::setw(14)
              << toString(op.type) << " Elem:" << std::setw(2) << op.element
              << " [" << std::setw(2) << op.lower() << "," << std::setw(2)
              << op.upper() << "] T:" << std::setw(2) << time_rank.time
              << " O:" << std::setw(2) << time_rank.rank << " ";

    int lower_pos = (op.lower() * TIMELINE_WIDTH) / max_time;
    int upper_pos = (op.upper() * TIMELINE_WIDTH) / max_time;
    int exec_pos = (time_rank.time * TIMELINE_WIDTH) / max_time;

    for (int i = 0; i <= TIMELINE_WIDTH; i++) {
      if (i == exec_pos) {
        out_stream << "│";
      } else if (i >= lower_pos && i <= upper_pos) {
        out_stream << "─";
      } else {
        out_stream << " ";
      }
    }
    out_stream << "\n";
  }

  out_stream << std::string(20, ' ');

  for (int i = 0; i <= TIMELINE_WIDTH; i += SCALE_INTERVAL) {
    int time_value = (i * max_time) / TIMELINE_WIDTH;
    out_stream << std::setw(SCALE_INTERVAL) << time_value;
  }
  out_stream << "\n";
}

void updateProgressBar(int trial, int num_trials);

} // namespace common::printer
