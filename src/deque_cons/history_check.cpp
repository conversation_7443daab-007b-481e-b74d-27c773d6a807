#include "common/history_check.hpp"
#include "cons.hpp"
#include <deque>

template <>
bool common::isOrderValid<deque_cons::DequeOp>(
    const std::vector<deque_cons::DequeOp>& ordered_ops) {
  std::deque<int> deque;
  for (const auto& op : ordered_ops) {
    switch (op.type) {
    case deque_cons::OpType::PUSH_FRONT:
      deque.push_front(op.element);
      break;
    case deque_cons::OpType::PUSH_BACK:
      deque.push_back(op.element);
      break;
    case deque_cons::OpType::POP_FRONT:
      if (deque.empty() || deque.front() != op.element) {
        return false;
      }
      deque.pop_front();
      break;
    case deque_cons::OpType::POP_BACK:
      if (deque.empty() || deque.back() != op.element) {
        return false;
      }
      deque.pop_back();
      break;
    case deque_cons::OpType::PEEK_FRONT:
      if (deque.empty() || deque.front() != op.element) {
        return false;
      }
      break;
    case deque_cons::OpType::PEEK_BACK:
      if (deque.empty() || deque.back() != op.element) {
        return false;
      }
      break;
    case deque_cons::OpType::EMPTY:
      if (!deque.empty()) {
        return false;
      }
      break;
    }
  }
  return deque.empty();
}

namespace deque_cons {

bool checkHistory(const std::vector<DequeOp>& ops,
                  const std::optional<TotalHistory>& result) {
  return common::checkHistory<DequeOp>(ops, result);
}

} // namespace deque_cons