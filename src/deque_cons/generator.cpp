#include "generator.hpp"
#include "common/generator_impl.hpp"
#include "common/sampling.hpp"
#include <algorithm>
#include <deque>
#include <numeric>
#include <stdexcept>

namespace deque_cons::generator {

class Tracker {
public:
  using OpType = deque_cons::OpType;

  [[nodiscard]] std::pair<ElemId, OpId> trackOperation(OpType op_type) {
    OpId op_id = next_id_++;

    switch (op_type) {
    case OpType::PUSH_FRONT: {
      elements_.push_front(next_element_);
      return {next_element_++, op_id};
    }
    case OpType::PUSH_BACK: {
      elements_.push_back(next_element_);
      return {next_element_++, op_id};
    }
    case OpType::POP_FRONT: {
      if (elements_.empty()) {
        throw std::runtime_error("Deque underflow during generation");
      }
      ElemId element = elements_.front();
      elements_.pop_front();
      return {element, op_id};
    }
    case OpType::POP_BACK: {
      if (elements_.empty()) {
        throw std::runtime_error("Deque underflow during generation");
      }
      ElemId element = elements_.back();
      elements_.pop_back();
      return {element, op_id};
    }
    case OpType::PEEK_FRONT: {
      if (elements_.empty()) {
        throw std::runtime_error("Cannot peek empty deque during generation");
      }
      return {elements_.front(), op_id};
    }
    case OpType::PEEK_BACK: {
      if (elements_.empty()) {
        throw std::runtime_error("Cannot peek empty deque during generation");
      }
      return {elements_.back(), op_id};
    }
    case OpType::EMPTY: {
      return {0, op_id};
    }
    }
    throw std::runtime_error("Unknown operation type");
  }

private:
  OpId next_id_{0};
  ElemId next_element_{1};
  std::deque<ElemId> elements_;
};

class OpTypeClassifier {
public:
  using OpType = deque_cons::OpType;

  static constexpr int NUM_TYPES = 7;

  [[nodiscard]] static common::generator::ContainerEffect
  getContainerEffect(OpType op_type) {
    switch (op_type) {
    case OpType::PUSH_FRONT:
    case OpType::PUSH_BACK:
      return ContainerEffect::ADD;
    case OpType::POP_FRONT:
    case OpType::POP_BACK:
      return ContainerEffect::REMOVE;
    case OpType::PEEK_FRONT:
    case OpType::PEEK_BACK:
      return ContainerEffect::PEEK;
    case OpType::EMPTY:
      return ContainerEffect::EMPTY;
    }
    assert(false);
    return ContainerEffect::PEEK;
  }
};

[[nodiscard]] std::vector<DequeOp> generateDequeOps(const InputConfig& config,
                                                    URNG& rng) {
  return common::generator::generateOps<DequeOp, OpTypeClassifier, Tracker>(
      config, rng);
}

} // namespace deque_cons::generator