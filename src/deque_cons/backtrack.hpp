#pragma once

#include "common/backtrack_impl.hpp"
#include "cons.hpp"
#include <deque>
#include <unordered_set>

namespace deque_cons {

class DequeSimulator {
public:
  using Op = DequeOp;

  bool canAddOperation(const DequeOp& op) {
    switch (op.type) {
    case OpType::PUSH_FRONT:
    case OpType::PUSH_BACK:
      return true;
    case OpType::POP_FRONT:
      return !deque_state.empty() && deque_state.front() == op.element;
    case OpType::POP_BACK:
      return !deque_state.empty() && deque_state.back() == op.element;
    case OpType::PEEK_FRONT:
      return !deque_state.empty() && deque_state.front() == op.element;
    case OpType::PEEK_BACK:
      return !deque_state.empty() && deque_state.back() == op.element;
    case OpType::EMPTY:
      return deque_state.empty();
    }
    return false;
  }

  void addOperation(const DequeOp& op) {
    switch (op.type) {
    case OpType::PUSH_FRONT:
      deque_state.push_front(op.element);
      break;
    case OpType::PUSH_BACK:
      deque_state.push_back(op.element);
      break;
    case OpType::POP_FRONT:
      deque_state.pop_front();
      break;
    case OpType::POP_BACK:
      deque_state.pop_back();
      break;
    case OpType::PEEK_FRONT:
    case OpType::PEEK_BACK:
    case OpType::EMPTY:
      // These operations don't modify state
      break;
    }
  }

  void removeOperation(const DequeOp& op) {
    switch (op.type) {
    case OpType::PUSH_FRONT:
      deque_state.pop_front();
      break;
    case OpType::PUSH_BACK:
      deque_state.pop_back();
      break;
    case OpType::POP_FRONT:
      deque_state.push_front(op.element);
      break;
    case OpType::POP_BACK:
      deque_state.push_back(op.element);
      break;
    case OpType::PEEK_FRONT:
    case OpType::PEEK_BACK:
    case OpType::EMPTY:
      // These operations don't modify state
      break;
    }
  }

  void clear() { deque_state.clear(); }

private:
  std::deque<ElemId> deque_state;
};

using Backtracker = common::solver::Backtracker<DequeSimulator>;

} // namespace deque_cons