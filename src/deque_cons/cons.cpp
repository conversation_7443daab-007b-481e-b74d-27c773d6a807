#include "cons.hpp"
#include <algorithm>

namespace deque_cons {

std::unordered_map<ElemId, ElemOps>
filterOperationsByElement(const std::vector<DequeOp>& operations) {
  std::unordered_map<ElemId, ElemOps> result;

  for (const auto& op : operations) {
    auto& elem_ops = result[op.element];

    switch (op.type) {
    case OpType::PUSH_FRONT:
      elem_ops.push_front = op;
      break;
    case OpType::PUSH_BACK:
      elem_ops.push_back = op;
      break;
    case OpType::POP_FRONT:
      elem_ops.pop_front = op;
      break;
    case OpType::POP_BACK:
      elem_ops.pop_back = op;
      break;
    case OpType::PEEK_FRONT:
      elem_ops.peeks_front.push_back(op);
      break;
    case OpType::PEEK_BACK:
      elem_ops.peeks_back.push_back(op);
      break;
    case OpType::EMPTY:
      break;
    }
  }

  return result;
}

void tightenBounds(ElemOps& element_ops) {
  // All operations must happen between pushes and pops
  if (element_ops.push_front) {
    if (element_ops.pop_front)
      raiseLowerBound(element_ops.pop_front->itv, element_ops.push_front->itv);
    if (element_ops.pop_back)
      raiseLowerBound(element_ops.pop_back->itv, element_ops.push_front->itv);
    for (auto& peek : element_ops.peeks_front)
      raiseLowerBound(peek.itv, element_ops.push_front->itv);
    for (auto& peek : element_ops.peeks_back)
      raiseLowerBound(peek.itv, element_ops.push_front->itv);
  }

  if (element_ops.push_back) {
    if (element_ops.pop_front)
      raiseLowerBound(element_ops.pop_front->itv, element_ops.push_back->itv);
    if (element_ops.pop_back)
      raiseLowerBound(element_ops.pop_back->itv, element_ops.push_back->itv);
    for (auto& peek : element_ops.peeks_front)
      raiseLowerBound(peek.itv, element_ops.push_back->itv);
    for (auto& peek : element_ops.peeks_back)
      raiseLowerBound(peek.itv, element_ops.push_back->itv);
  }

  // Pushes must happen before all operations
  if (element_ops.push_front) {
    if (element_ops.pop_front)
      reduceUpperBound(element_ops.push_front->itv, element_ops.pop_front->itv);
    if (element_ops.pop_back)
      reduceUpperBound(element_ops.push_front->itv, element_ops.pop_back->itv);
    for (const auto& peek : element_ops.peeks_front)
      reduceUpperBound(element_ops.push_front->itv, peek.itv);
    for (const auto& peek : element_ops.peeks_back)
      reduceUpperBound(element_ops.push_front->itv, peek.itv);
  }

  if (element_ops.push_back) {
    if (element_ops.pop_front)
      reduceUpperBound(element_ops.push_back->itv, element_ops.pop_front->itv);
    if (element_ops.pop_back)
      reduceUpperBound(element_ops.push_back->itv, element_ops.pop_back->itv);
    for (const auto& peek : element_ops.peeks_front)
      reduceUpperBound(element_ops.push_back->itv, peek.itv);
    for (const auto& peek : element_ops.peeks_back)
      reduceUpperBound(element_ops.push_back->itv, peek.itv);
  }
}

void tightenBounds(std::unordered_map<ElemId, ElemOps>& element_ops) {
  for (auto& [_, ops] : element_ops) {
    tightenBounds(ops);
  }
}

bool areItvsValid(const ElemOps& element_ops) {
  auto isValidOp = [](const auto& op) { return isValid(op.itv); };

  if (element_ops.push_front && !isValidOp(*element_ops.push_front))
    return false;
  if (element_ops.push_back && !isValidOp(*element_ops.push_back))
    return false;
  if (element_ops.pop_front && !isValidOp(*element_ops.pop_front))
    return false;
  if (element_ops.pop_back && !isValidOp(*element_ops.pop_back))
    return false;

  return std::all_of(element_ops.peeks_front.begin(),
                     element_ops.peeks_front.end(), isValidOp) &&
         std::all_of(element_ops.peeks_back.begin(),
                     element_ops.peeks_back.end(), isValidOp);
}

bool areItvsValid(const std::unordered_map<ElemId, ElemOps>& element_ops) {
  return std::all_of(
      element_ops.begin(), element_ops.end(),
      [](const auto& pair) { return areItvsValid(pair.second); });
}

} // namespace deque_cons