#pragma once

#include "common/generator.hpp"
#include "common/sampling.hpp"
#include "cons.hpp"
#include <random>
#include <vector>

namespace deque_cons::generator {
using namespace common::generator;

/**
 * @brief Generates a sequence of deque operations based on the configuration
 *
 * @param config Generator configuration parameters
 * @return std::vector<DequeOp> Sequence of generated operations
 * @throws std::invalid_argument if configuration is invalid
 * @throws std::runtime_error if generation fails (e.g., deque underflow)
 */
[[nodiscard]] std::vector<DequeOp> generateDequeOps(const InputConfig& config,
                                                    URNG& rng);

} // namespace deque_cons::generator