#include "testing.hpp"
#include "common/testing_impl.hpp"
#include "history_check.hpp"
#include "printer.hpp"
#include <initializer_list>
#include <iomanip>
#include <thread>

namespace deque_cons {

using TestGenerator =
    common::testing::ParamCombinator<[](int trial, int max_ops, double variance,
                                        int permutation) {
      auto config =
          common::generator::InputConfig{.max_ops = max_ops,
                                         .variance_factor = variance,
                                         .permutation_factor = permutation};
      URNG rng(trial);
      return generator::generateDequeOps(config, rng);
    }>;

std::optional<std::vector<DequeOp>>
findCounterexample(const Solver& solver,
                   const common::generator::InputConfig& config,
                   int num_trials) {
  return common::testing::findCounterexample<DequeOp, TestGenerator>(
      solver, config, num_trials);
}

void benchmarkConfigurations(
    const Solver& solver, const common::testing::TestConfigParamList& config) {
  common::testing::benchmarkConfigurations<DequeOp, TestGenerator>(solver,
                                                                   config);
}

std::optional<std::vector<DequeOp>>
testSolverTime(const Solver& solver,
               const common::generator::InputConfig& config,
               std::chrono::milliseconds time_threshold, int num_trials) {
  return common::testing::testSolverTime<DequeOp, TestGenerator>(
      solver, config, time_threshold, num_trials);
}

std::optional<
    std::tuple<generator::InputConfig, std::vector<DequeOp>,
               std::optional<TotalHistory>, std::optional<TotalHistory>>>
compareSolvers(const Solver& solver1, const Solver& solver2,
               const common::testing::TestConfigParamList& config) {
  return common::testing::compareSolvers<DequeOp, TestGenerator>(
      solver1, solver2, config);
}

} // namespace deque_cons