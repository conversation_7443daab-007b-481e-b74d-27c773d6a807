#pragma once

#include "cons.hpp"
#include "queue_cons/cons.hpp"
#include "stack_cons/cons.hpp"
#include <list>
#include <optional>
#include <unordered_set>

namespace deque_cons {

template <typename S>
concept StackSolver = requires(S s, s::unordered_map<ElemId, stack_cons::ElemOps>td ops) {
  TODO
  { s.constructTotalOrder(ops) } -> std::convertible_to<std::optional<std::list<OpId>>>;
};

template <typename Q>
concept QueueSolver = requires(Q q, std::unordered_map<ElemId, queue_cons::ElemOps> ops) {
  TODO
  { q.constructTotalOrder(ops) } -> std::convertible_to<std::optional<std::list<OpId>>>;
};

template <StackSolver S, QueueSolver Q>
class NoPeek : public HistoryConstructor {
public:
  explicit NoPeek(const std::vector<DequeOp>& ops) : HistoryConstructor(ops) {}

  std::optional<std::list<OpId>> constructTotalOrder() override {
    splitOpsByDirection();
    
    if (!constructStackHistories()) {
      return std::nullopt;
    }
    
    if (!constructQueueHistories()) {
      return std::nullopt;
    }

    return mergeHistories();
  }

  std::optional<TotalHistory> constructHistory() override {
    return this->constructTotalOrder().transform(
        [this](const auto& order) { return convertOrderToHistory(order, ops); });
  }

private:
  stack_cons::ElemOpsMap front_to_front_ops;
  stack_cons::ElemOpsMap back_to_back_ops;
  queue_cons::ElemOpsMap front_to_back_ops;
  queue_cons::ElemOpsMap back_to_front_ops;

  std::vector<Time> times;
  std::unordered_set<Time> stack_elem_in_front_times;
  std::unordered_set<Time> stack_elem_in_back_times;
  
  TotalHistory front_to_front_history;
  TotalHistory back_to_back_history;
  TotalHistory front_to_back_history;
  TotalHistory back_to_front_history;

  void splitOpsByDirection() {
    auto op_map = filterOperationsByElement(ops);

    for (const auto& [id, op] : elem_ops) {
      if (op.push_front && op.pop_front) {
        front_to_front_ops[id] = {op.push_front.value(), op.pop_front.value()};
      } else if (op.push_back && op.pop_back) {
        back_to_back_ops[id] = {op.push_back.value(), op.pop_back.value()};
      } else if (op.push_front && op.pop_back) {
        front_to_back_ops[id] = {op.push_front.value(), op.pop_back.value()};
      } else if (op.push_back && op.pop_front) {
        back_to_front_ops[id] = {op.push_back.value(), op.pop_front.value()};
      } else {
        throw std::runtime_error("Invalid operations: missing push or pop for element " +
                                 std::to_string(id));
      }
    }
  }
 
  [[nodiscard]] static inline stack_cons::StackOp toStackOp(const DequeOp& op) noexcept {
    return {op.id, op.type, op.element, op.itv};
  }

  void constructStackHistories() {
    todo();
  }

  void constructQueueHistories() {
    markStackPresenceAsUnassignable();
    partitionIntoIntervals();
    solveAllIntervals();
  }

  std::optional<std::list<OpId>> mergeHistories() {
    todo();
  }
};

} // namespace deque_cons
