#pragma once

#include "cons.hpp"
#include "queue_cons/cons.hpp"
#include "stack_cons/cons.hpp"
#include <list>
#include <optional>

namespace deque_cons {

class HasPeek : public std::exception {
  [[nodiscard]] const char* what() const noexcept override {
    return "Operation sequence contains peek operations";
  }
};

template <typename S>
concept StackSolver = requires(S s, s::unordered_map<ElemId, stack_cons::ElemOps>td ops) {
  TODO
  { s.constructTotalOrder(ops) } -> std::convertible_to<std::optional<std::list<OpId>>>;
};

template <typename Q>
concept QueueSolver = requires(Q q, std::unordered_map<ElemId, queue_cons::ElemOps> ops) {
  TODO
  { q.constructTotalOrder(ops) } -> std::convertible_to<std::optional<std::list<OpId>>>;
};

template <StackSolver S, QueueSolver Q>
class NoPeek : public HistoryConstructor {
public:
  explicit NoPeek(const std::vector<DequeOp>& ops) : HistoryConstructor(ops) {}

  std::optional<std::list<OpId>> constructTotalOrder() override {
    splitOpsToStackAndQueue();
    constructStackHistory();
    constructQueueHistory();
    return mergeStackAndQueue();
  }

  std::optional<TotalHistory> constructHistory() override {
    return this->constructTotalOrder().transform(
        [this](const auto& order) { return convertOrderToHistory(order, ops); });
  }

private:
  stack_cons::ElemOpsMap ff_ops;
  stack_cons::ElemOpsMap bb_ops;
  queue_cons::ElemOpsMap fb_ops;
  queue_cons::ElemOpsMap fb_ops;
};

} // namespace deque_cons
