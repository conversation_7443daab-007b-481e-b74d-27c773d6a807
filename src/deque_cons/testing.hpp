#pragma once

#include "common/testing.hpp"
#include "cons.hpp"
#include "generator.hpp"
#include <algorithm>
#include <cassert>
#include <functional>
#include <iostream>
#include <vector>

namespace deque_cons {

const common::testing::TestConfigParamList DEFAULT_TEST_CONFIG = {
    .trials_per_config = 100,
    .max_ops_values = {5, 10, 15, 20, 25, 50, 100},
    .variance_factors = {2.0, 5.0, 10.0, 50.0},
    .permutation_factors = {0, 1, 2, 5, 10}};

using Solver = common::testing::Solver<DequeOp>;

/**
 * Attempts to find a counterexample for the given solver.
 *
 * @param solver The solver to test
 * @param config Configuration for generating test cases
 * @param num_trials Number of test cases to generate
 * @return Optional vector of operations that form a counterexample
 */
std::optional<std::vector<DequeOp>> findCounterexample(
    const Solver& solver,
    const common::generator::InputConfig& config = {.max_ops = 10,
                                                    .variance_factor = 2,
                                                    .permutation_factor = 0},
    int num_trials = 1000);

/**
 * Benchmarks the solver across different configurations.
 *
 * @param solver The solver to benchmark
 * @param config Test configuration parameters
 */
void benchmarkConfigurations(
    const Solver& solver,
    const common::testing::TestConfigParamList& config = DEFAULT_TEST_CONFIG);

/**
 * Tests if the solver exceeds a time threshold on any test case.
 *
 * @param solver The solver to test
 * @param config Configuration for generating test cases
 * @param time_threshold Maximum allowed time per test case
 * @param num_trials Number of test cases to generate
 * @return Optional vector of operations that caused timeout
 */
std::optional<std::vector<DequeOp>> testSolverTime(
    const Solver& solver,
    const common::generator::InputConfig& config = {.max_ops = 10,
                                                    .variance_factor = 2,
                                                    .permutation_factor = 0},
    std::chrono::milliseconds time_threshold = std::chrono::milliseconds(1000),
    int num_trials = 100);

/**
 * Compares two solvers for consistency and performance.
 *
 * @param solver1 First solver to compare
 * @param solver2 Second solver to compare
 * @param config Test configuration parameters
 * @return Optional tuple containing configuration, operations, and results that
 * differ
 */
std::optional<
    std::tuple<generator::InputConfig, std::vector<DequeOp>,
               std::optional<TotalHistory>, std::optional<TotalHistory>>>
compareSolvers(
    const Solver& solver1, const Solver& solver2,
    const common::testing::TestConfigParamList& config = DEFAULT_TEST_CONFIG);

} // namespace deque_cons