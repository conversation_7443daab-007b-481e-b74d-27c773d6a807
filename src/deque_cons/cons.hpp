#pragma once

#include <cassert>
#include <common/cons.hpp>
#include <common/interval.hpp>
#include <list>
#include <optional>
#include <string>
#include <unordered_map>
#include <vector>

namespace deque_cons {

using namespace common;

enum class OpType {
  PUSH_FRONT,
  PUSH_BACK,
  POP_FRONT,
  POP_BACK,
  PEEK_FRONT,
  PEEK_BACK,
  EMPTY
};

struct DequeOp {
  OpId id;
  OpType type;
  ElemId element;
  Interval itv;

  Time lower() const { return itv.l; }
  Time upper() const { return itv.r; }

  DequeOp(OpId id, OpType type, ElemId element, Interval itv)
      : id(id), type(type), element(element), itv(itv) {
    assert(isValid(itv));
  }
};

struct ElemOps {
  std::optional<DequeOp> push_front;
  std::optional<DequeOp> push_back;
  std::optional<DequeOp> pop_front;
  std::optional<DequeOp> pop_back;
  std::vector<DequeOp> peeks_front;
  std::vector<DequeOp> peeks_back;

  ElemOps() = default;
};

using ElemOpsMap = std::unordered_map<ElemId, ElemOps>;
using HistoryConstructor = common::HistoryConstructor<DequeOp>;

std::unordered_map<ElemId, ElemOps>
filterOperationsByElement(const std::vector<DequeOp>& operations);

void tightenBounds(ElemOps& element_ops);
void tightenBounds(std::unordered_map<ElemId, ElemOps>& element_ops);

[[nodiscard]] bool areItvsValid(const ElemOps& element_ops);
[[nodiscard]] bool
areItvsValid(const std::unordered_map<ElemId, ElemOps>& element_ops);

} // namespace deque_cons