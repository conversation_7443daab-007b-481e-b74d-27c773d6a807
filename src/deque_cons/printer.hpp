#pragma once

#include "cons.hpp"
#include <string>

namespace deque_cons {

/**
 * Converts a deque operation type to its string representation.
 *
 * @param op The operation type to convert
 * @return String representation of the operation type
 */
inline std::string toString(OpType op) {
  switch (op) {
  case OpType::PUSH_FRONT:
    return "PUSH_FRONT";
  case OpType::PUSH_BACK:
    return "PUSH_BACK";
  case OpType::POP_FRONT:
    return "POP_FRONT";
  case OpType::POP_BACK:
    return "POP_BACK";
  case OpType::PEEK_FRONT:
    return "PEEK_FRONT";
  case OpType::PEEK_BACK:
    return "PEEK_BACK";
  case OpType::EMPTY:
    return "EMPTY";
  }
  return "???";
}

} // namespace deque_cons