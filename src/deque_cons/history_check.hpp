#pragma once

#include "cons.hpp"
#include <optional>
#include <vector>

namespace deque_cons {

/**
 * Checks if the given history is valid for the deque operations.
 *
 * @param ops Vector of deque operations
 * @param result Optional total history to check
 * @return true if the history is valid, false otherwise
 */
bool checkHistory(const std::vector<DequeOp>& ops,
                  const std::optional<TotalHistory>& result);

} // namespace deque_cons