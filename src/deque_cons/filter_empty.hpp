#pragma once

#include "common/filter_empty.hpp"
#include "cons.hpp"

namespace deque_cons {

template <typename S>
  requires requires(S s, ElemOpsMap elem_ops) {
    {
      s.constructTotalOrder(elem_ops)
    } -> std::convertible_to<std::optional<std::list<OpId>>>;
  }
class FilterEmptyDefs {
public:
  using Op = DequeOp;
  using ElemOps = ElemOps;
  using ElemOpsMap = std::unordered_map<ElemId, ElemOps>;
  using Solver = S;

  static bool isEmptyOp(const Op& op) { return op.type == OpType::EMPTY; }

  static ElemOpsMap filterOpsByElem(const std::vector<Op>& ops) {
    return filterOperationsByElement(ops);
  }

  static Interval getInItv(const ElemOps& elem_ops) {
    Time lower = std::numeric_limits<Time>::max();
    Time upper = std::numeric_limits<Time>::min();

    // Check push operations
    if (elem_ops.push_front) {
      lower = std::min(lower, elem_ops.push_front->upper());
      upper = std::max(upper, elem_ops.push_front->lower());
    }
    if (elem_ops.push_back) {
      lower = std::min(lower, elem_ops.push_back->upper());
      upper = std::max(upper, elem_ops.push_back->lower());
    }

    // Check pop operations
    if (elem_ops.pop_front) {
      lower = std::min(lower, elem_ops.pop_front->upper());
      upper = std::max(upper, elem_ops.pop_front->lower());
    }
    if (elem_ops.pop_back) {
      lower = std::min(lower, elem_ops.pop_back->upper());
      upper = std::max(upper, elem_ops.pop_back->lower());
    }

    // Check peek operations
    for (const auto& peek : elem_ops.peeks_front) {
      lower = std::min(lower, peek.upper());
      upper = std::max(upper, peek.lower());
    }
    for (const auto& peek : elem_ops.peeks_back) {
      lower = std::min(lower, peek.upper());
      upper = std::max(upper, peek.lower());
    }

    return {lower + 1, upper - 1};
  }
};

template <typename S>
  requires requires(S s, ElemOpsMap elem_ops) {
    {
      s.constructTotalOrder(elem_ops)
    } -> std::convertible_to<std::optional<std::list<OpId>>>;
  }
using FilterEmpty = common::filter_empty::FilterEmpty<FilterEmptyDefs<S>>;

} // namespace deque_cons