#include "common/history_check.hpp"
#include "cons.hpp"

template <>
bool common::isOrderValid<stack_cons::StackOp>(
    const std::vector<stack_cons::StackOp>& ordered_ops) {
  std::vector<int> stack;
  for (const auto& op : ordered_ops) {
    switch (op.type) {
    case stack_cons::OpType::PUSH:
      stack.push_back(op.element);
      break;
    case stack_cons::OpType::POP:
      if (stack.empty() || stack.back() != op.element) {
        return false;
      }
      stack.pop_back();
      break;
    case stack_cons::OpType::PEEK:
      if (stack.empty() || stack.back() != op.element) {
        return false;
      }
      break;
    case stack_cons::OpType::EMPTY:
      if (!stack.empty()) {
        return false;
      }
      break;
    }
  }
  return stack.empty();
}

namespace stack_cons {

bool checkHistory(const std::vector<StackOp>& ops,
                  const std::optional<TotalHistory>& result) {
  return common::checkHistory<StackOp>(ops, result);
}

} // namespace stack_cons
