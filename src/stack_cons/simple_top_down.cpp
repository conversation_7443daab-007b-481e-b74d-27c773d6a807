/**
 * @file simple_top_down.cpp
 * @brief Implementation of the SimpleTopDown class
 *
 * This file contains the implementation of the SimpleTopDown class, which
 * constructs a valid total order for stack operations using a top-down approach.
 */
#include "common/banned_intervals.hpp"
#include "simple_top_down.hpp"
#include "filter_empty.hpp"
#include <algorithm>
#include <cassert>
#include <iostream>
#include <numeric>
#include <map>
#include <unordered_set>
#include <ranges>

namespace stack_cons {

namespace {
class SimpleTopDownNoEmpty {
public:
  explicit SimpleTopDownNoEmpty() = default;

  std::optional<std::list<OpId>>
  constructTotalOrder(const ElemOpsMap& elem_ops);

private:
  std::unordered_map<ElemId, ElemOps> elem_ops;
  Time time_lower_bound;
  Time time_upper_bound;

  std::map<Time, std::list<OpId>> order_by_time{};
  std::unordered_set<ElemId> remaining_elems{};

  bool searchHistory();
  bool coverElementsTopDown();
  bool tryToCoverElement(int elem);

  bool findTimesThenUpdate(int elem);

  using ElemExactTimes = std::tuple<Time, Time, std::vector<Time>>;

  /* Attempts to find exact times for an element's operations. */
  std::optional<ElemExactTimes> findExactTimes(int elem);

  /* Returns the set of intervals banned for an element. */
  BannedIntervals getBannedIntervals(int elem);

  std::vector<Interval> getBannedIntervalsFromOtherElems(int elem);

  void addBoundsToBanned(std::vector<Interval>& banned, int elem);

  /* Returns the interval in which an element must be on the stack. */
  inline Interval inStackItv(const ElemOps& ops) {
    return {ops.push.upper() + 1, ops.pop.lower() - 1};
  }

  /* Updates the order_by_time map with the exact times for an element's ops. */
  void updateOrderByTime(int elem, const ElemExactTimes& exact_times);

  void updateOrderByTime(int elem, Time time);
};

std::optional<std::list<OpId>>
SimpleTopDownNoEmpty::constructTotalOrder(const ElemOpsMap& elem_ops) {
  this->elem_ops = elem_ops;

  if (!searchHistory()) {
    return std::nullopt;
  }

  std::list<OpId> total_order;
  for (const auto& [_, ops] : order_by_time) {
    total_order.insert(total_order.end(), ops.cbegin(), ops.cend());
  }
  return total_order;
}

bool SimpleTopDownNoEmpty::searchHistory() {
  // Tighten bounds
  tightenBounds(elem_ops);
  if (!areItvsValid(elem_ops)) {
    return false;
  }

  if (coverElementsTopDown()) {
    return true;
  } else {
    return false;
  }
}

bool SimpleTopDownNoEmpty::coverElementsTopDown() {
  remaining_elems.clear();
  for (const auto& [elem_id, _] : elem_ops) {
    remaining_elems.insert(elem_id);
  }

  while (!remaining_elems.empty()) {
    bool covered = false;
    for (int elem : remaining_elems) {
      if (tryToCoverElement(elem)) {
        covered = true;
        break;
      }
    }
    if (!covered) {
      return false; // No element could be covered, solution not found
    }
  }
  return true;
}

bool SimpleTopDownNoEmpty::tryToCoverElement(int elem) {
  if (!findTimesThenUpdate(elem)) {
    return false;
  }

  remaining_elems.erase(
      std::find(remaining_elems.cbegin(), remaining_elems.cend(), elem));

  return true;
}

bool SimpleTopDownNoEmpty::findTimesThenUpdate(int elem) {
  auto exact_times = findExactTimes(elem);
  if (!exact_times) {
    return false;
  }

  updateOrderByTime(elem, exact_times.value());
  return true;
}

std::optional<SimpleTopDownNoEmpty::ElemExactTimes>
SimpleTopDownNoEmpty::findExactTimes(int elem) {
  auto banned_intervals = getBannedIntervals(elem);

  auto push_time = banned_intervals.findValidPoint(elem_ops.at(elem).push.itv);
  if (!push_time) {
    return std::nullopt;
  }

  auto pop_time = banned_intervals.findValidPoint(elem_ops.at(elem).pop.itv);
  if (!pop_time) {
    return std::nullopt;
  }

  std::vector<Time> peek_times;
  for (const auto& peek : elem_ops.at(elem).peeks) {
    if (canExec(peek, push_time.value())) {
      peek_times.push_back(push_time.value());
    } else if (canExec(peek, pop_time.value())) {
      peek_times.push_back(pop_time.value());
    } else {
      auto peek_time = banned_intervals.findValidPoint(peek.itv);
      if (!peek_time) {
        return std::nullopt;
      }

      assert(push_time.value() <= peek_time.value() &&
             peek_time.value() <= pop_time.value());
      peek_times.push_back(peek_time.value());
    }
  }

  return std::make_tuple(push_time.value(), pop_time.value(), peek_times);
}

BannedIntervals SimpleTopDownNoEmpty::getBannedIntervals(int elem) {
  std::vector<Interval> banned = getBannedIntervalsFromOtherElems(elem);

  addBoundsToBanned(banned, elem);

  return BannedIntervals(banned);
}

std::vector<Interval>
SimpleTopDownNoEmpty::getBannedIntervalsFromOtherElems(int elem) {
  std::vector<Interval> itvs;
  for (auto other_elem : remaining_elems) {
    if (other_elem == elem) {
      continue;
    }
    itvs.push_back(inStackItv(elem_ops.at(other_elem)));
  }
  return itvs;
}

void SimpleTopDownNoEmpty::addBoundsToBanned(std::vector<Interval>& banned,
                                             int elem) {
  auto right_bound_it =
      order_by_time.upper_bound(elem_ops.at(elem).push.upper());
  if (right_bound_it != order_by_time.end()) {
    banned.push_back({right_bound_it->first + 1, time_upper_bound});
  }

  if (right_bound_it != order_by_time.begin()) {
    auto left_bound_it = std::prev(right_bound_it);
    banned.push_back({time_lower_bound, left_bound_it->first - 1});
  }
}

void SimpleTopDownNoEmpty::updateOrderByTime(
    int elem, const ElemExactTimes& exact_times) {
  auto [push_time, pop_time, peek_times] = exact_times;

  if (push_time == pop_time) {
    return updateOrderByTime(elem, push_time);
  }

  order_by_time[push_time].push_back(elem_ops.at(elem).push.id);
  order_by_time[pop_time].push_front(elem_ops.at(elem).pop.id);

  for (size_t i = 0; i < peek_times.size(); ++i) {
    if (peek_times[i] == push_time) {
      order_by_time[push_time].push_back(elem_ops.at(elem).peeks[i].id);
    } else {
      order_by_time[peek_times[i]].push_front(elem_ops.at(elem).peeks[i].id);
    }
  }
}

void SimpleTopDownNoEmpty::updateOrderByTime(int elem, Time time) {
  order_by_time[time].push_back(elem_ops.at(elem).push.id);

  for (const auto& peek : elem_ops.at(elem).peeks) {
    assert(canExec(peek, time));
    order_by_time[time].push_back(peek.id);
  }

  assert(canExec(elem_ops.at(elem).pop, time));
  order_by_time[time].push_back(elem_ops.at(elem).pop.id);
}

} // namespace

SimpleTopDown::SimpleTopDown(const std::vector<StackOp>& ops) : HistoryConstructor(ops) {}

std::optional<std::list<OpId>> SimpleTopDown::constructTotalOrder() {
  return FilterEmpty<SimpleTopDownNoEmpty>().constructTotalOrder(ops);
}

std::optional<TotalHistory> SimpleTopDown::constructHistory() {
  return SimpleTopDown::constructTotalOrder()
    .transform([this](const auto& order) {
      return convertOrderToHistory(order, ops);
    });
}

} // namespace stack_cons
