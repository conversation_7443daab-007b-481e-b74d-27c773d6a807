#pragma once

#include "common/backtrack_impl.hpp"
#include "cons.hpp"
#include <stack>
#include <unordered_set>

namespace stack_cons {

class StackSimulator {
public:
    using Op = StackOp;

    bool canAddOperation(const StackOp& op) {
        switch (op.type) {
        case OpType::PUSH:
            return true;
        case OpType::POP:
        case OpType::PEEK:
            return !stack_state.empty() && stack_state.top() == op.element;
        case OpType::EMPTY:
            return stack_state.empty();
        }
        return false;
    }

    void addOperation(const StackOp& op) {
        if (op.type == OpType::PUSH) {
            stack_state.push(op.element);
        } else if (op.type == OpType::POP) {
            stack_state.pop();
        }
        // EMPTY and PEEK don't modify state
    }

    void removeOperation(const StackOp& op) {
        if (op.type == OpType::PUSH) {
            stack_state.pop();
        } else if (op.type == OpType::POP) {
            stack_state.push(op.element);
        }
        // EMPTY and PEEK don't modify state
    }

    void clear() {
        while (!stack_state.empty()) {
            stack_state.pop();
        }
    }

private:
    std::stack<ElemId> stack_state;
};

using Backtracker = common::solver::Backtracker<StackSimulator>;
} // namespace stack_cons
