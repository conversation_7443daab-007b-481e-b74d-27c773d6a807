#pragma once

#include <algorithm>
#include <cassert>
#include <common/cons.hpp>
#include <common/interval.hpp>
#include <deque>
#include <list>
#include <optional>
#include <string>
#include <unordered_map>
#include <vector>

namespace stack_cons {

using namespace common;

enum class OpType { PUSH, POP, PEEK, EMPTY };

struct StackOp {
  OpId id;
  OpType type;
  ElemId element;
  Interval itv;

  Time lower() const { return itv.l; }
  Time upper() const { return itv.r; }

  StackOp(OpId id, OpType type, ElemId element, Interval itv)
      : id(id), type(type), element(element), itv(itv) {
    assert(isValid(itv));
  }
};

struct ElemOps {
  StackOp push;
  StackOp pop;
  std::vector<StackOp> peeks;
};

using ElemOpsMap = std::unordered_map<ElemId, ElemOps>;

using HistoryConstructor = common::HistoryConstructor<StackOp>;

/**
 * Filters operations by element and groups them into ElementOperations.
 *
 * @param operations The map of operations to filter.
 * @return A map of ElementOperations, keyed by element ID.
 */
std::unordered_map<ElemId, ElemOps>
filterOperationsByElement(const std::vector<StackOp>& operations);

void tightenBounds(ElemOps& element_ops);

void tightenBounds(std::unordered_map<ElemId, ElemOps>& element_ops);

/* Checks if all push/pop/peek intervals of an element are valid. */
[[nodiscard]] bool areItvsValid(const ElemOps& element_ops);

/* Checks if all push/pop/peek intervals of all elements are valid. */
[[nodiscard]] bool
areItvsValid(const std::unordered_map<ElemId, ElemOps>& element_ops);

/* Checks if an operation must be executed in the interval itv. */
[[nodiscard]] inline bool isOpInItv(const Interval& itv, const StackOp& op) {
  return itv.l < op.lower() && op.upper() < itv.r;
}

} // namespace stack_cons
