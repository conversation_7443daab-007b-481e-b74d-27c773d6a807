/**
 * @file cons.cpp
 * @brief Implementation of stack consistency checking functions
 *
 * This file contains the implementation of functions for checking stack
 * consistency and constructing valid histories.
 */
#include "cons.hpp"
#include <algorithm>

namespace stack_cons {

std::unordered_map<ElemId, ElemOps>
filterOperationsByElement(const std::vector<StackOp>& operations) {
  std::unordered_map<ElemId, std::optional<StackOp>> push_ops;
  std::unordered_map<ElemId, std::optional<StackOp>> pop_ops;
  std::unordered_map<ElemId, std::vector<StackOp>> peek_ops;

  for (const auto& op : operations) {
    switch (op.type) {
    case OpType::PUSH:
      push_ops[op.element] = op;
      break;
    case OpType::POP:
      pop_ops[op.element] = op;
      break;
    case OpType::PEEK:
      peek_ops[op.element].push_back(op);
      break;
    case OpType::EMPTY:
      assert(false);
    }
  }

  std::unordered_map<ElemId, ElemOps> result;
  for (const auto& [elem_id, push_op] : push_ops) {
    if (!pop_ops.contains(elem_id) || !pop_ops[elem_id]) {
      throw std::runtime_error(
          "Invalid operations: missing push or pop for element " +
          std::to_string(elem_id));
    }

    result.emplace(elem_id, ElemOps{push_op.value(), pop_ops[elem_id].value(),
                                    peek_ops[elem_id]});
  }

  return result;
}

void tightenBounds(ElemOps& element_ops) {
  auto& push = element_ops.push;
  auto& pop = element_ops.pop;
  auto& peeks = element_ops.peeks;

  // All operations must happen after push
  for (auto& peek : peeks) {
    raiseLowerBound(peek.itv, push.itv);
  }
  raiseLowerBound(pop.itv, push.itv);

  // Push must happen before all operations
  for (auto& peek : peeks) {
    reduceUpperBound(push.itv, peek.itv);
  }
  reduceUpperBound(push.itv, pop.itv);

  // All operations must happen before pop
  for (auto& peek : peeks) {
    reduceUpperBound(peek.itv, pop.itv);
  }

  // Pop must happen after all operations
  for (auto& peek : peeks) {
    raiseLowerBound(pop.itv, peek.itv);
  }
}

void tightenBounds(std::unordered_map<ElemId, ElemOps>& element_ops) {
  for (auto& [_, ops] : element_ops) {
    tightenBounds(ops);
  }
}

[[nodiscard]] bool areItvsValid(const ElemOps& element_ops) {
  auto& push = element_ops.push;
  auto& pop = element_ops.pop;
  auto& peeks = element_ops.peeks;

  return isValid(push.itv) && isValid(pop.itv) &&
         std::all_of(peeks.cbegin(), peeks.cend(),
                     [](const auto& op) { return isValid(op.itv); });
}

[[nodiscard]] bool
areItvsValid(const std::unordered_map<ElemId, ElemOps>& element_ops) {
  for (const auto& [_, ops] : element_ops) {
    if (!areItvsValid(ops)) {
      return false;
    }
  }
  return true;
}

} // namespace stack_cons
