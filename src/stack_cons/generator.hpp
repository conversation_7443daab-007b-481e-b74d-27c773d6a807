#pragma once

#include "common/generator.hpp"
#include "common/sampling.hpp"
#include "cons.hpp"
#include <array>
#include <iostream>
#include <random>

namespace stack_cons::generator {

using namespace common::generator;

/**
 * @brief Generates a sequence of stack operations based on the configuration
 *
 * @param config Generator configuration parameters
 * @return std::vector<Operation> Sequence of generated operations
 * @throws std::invalid_argument if configuration is invalid
 * @throws std::runtime_error if generation fails (e.g., stack underflow)
 */
[[nodiscard]] std::vector<StackOp> generateStackOps(const InputConfig& config,
                                                    URNG& rng);

} // namespace stack_cons::generator
