#pragma once

#include "common/filter_empty.hpp"
#include "cons.hpp"

namespace stack_cons {

template <typename S>
  requires requires(S s, ElemOpsMap elem_ops) {
    {
      s.constructTotalOrder(elem_ops)
    } -> std::convertible_to<std::optional<std::list<OpId>>>;
  }
class FilterEmptyDefs {
public:
  using Op = StackOp;
  using ElemOps = ElemOps;
  using ElemOpsMap = std::unordered_map<ElemId, ElemOps>;
  using Solver = S;

  static bool isEmptyOp(const Op& op) { return op.type == OpType::EMPTY; }

  static ElemOpsMap filterOpsByElem(const std::vector<Op>& ops) {
    return filterOperationsByElement(ops);
  }

  static Interval getInItv(const ElemOps& elem_ops) {
    Time lower = std::min(elem_ops.push.upper(), elem_ops.pop.upper());
    Time upper = std::max(elem_ops.push.lower(), elem_ops.pop.lower());

    for (const auto& peek : elem_ops.peeks) {
      lower = std::min(lower, peek.upper());
      upper = std::max(upper, peek.lower());
    }

    return {lower + 1, upper - 1};
  }
};

template <typename S>
  requires requires(S s, ElemOpsMap elem_ops) {
    {
      s.constructTotalOrder(elem_ops)
    } -> std::convertible_to<std::optional<std::list<OpId>>>;
  }
using FilterEmpty = common::filter_empty::FilterEmpty<FilterEmptyDefs<S>>;

} // namespace stack_cons
