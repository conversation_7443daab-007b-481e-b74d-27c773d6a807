#pragma once

#include "common/interval.hpp"
#include "cons.hpp"
#include <vector>
#include <list>
#include <optional>

namespace stack_cons {

class SimpleDP : public HistoryConstructor {
public:
  explicit SimpleDP(const std::vector<StackOp>& ops);

  std::optional<std::list<OpId>> constructTotalOrder() override;
  std::optional<TotalHistory> constructHistory() override;
};
} // namespace stack_cons
