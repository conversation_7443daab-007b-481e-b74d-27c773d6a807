#include "simple_dp.hpp"
#include "filter_empty.hpp"
#include <algorithm>
#include <cassert>
#include <ranges>

namespace stack_cons {

namespace {
class SimpleDPNoEmpty {
public:
  explicit SimpleDPNoEmpty() = default;

  [[nodiscard]] std::optional<std::list<OpId>>
  constructTotalOrder(const ElemOpsMap& ops);

private:
  ElemOpsMap elem_ops;

  Time time_lower_bound;
  Time time_upper_bound;

  std::unordered_map<Interval, std::optional<std::list<OpId>>>
      memo_cons_total_order_in{};

  void initializeTimeBounds();

  /**
   * Attempts to construct a valid total order for the operations in
   * ElemsIn(itv).
   * @return List of operation IDs in a valid total order
   */
  std::optional<std::list<OpId>> consIn(Interval itv);

  /**
   * Checks if all elements in elems can be executed within the interval itv.
   */
  bool canFit(Interval itv, const std::vector<ElemId>& elems);

  /** Checks if an element can be executed within the interval itv. */
  bool canFit(Interval itv, ElemId elem);

  /**
   * Attempts to construct a valid total order for the operations in
   * ElemsIn(itv) by splitting the interval into [l, m] and [m, r] for some m.
   * @return List of operation IDs in a valid total order
   */
  std::optional<std::list<OpId>> consBySplit(Interval itv,
                                             const std::vector<ElemId>& elems);

  /**
   * Finds a split point m such that the interval itv can be split into [l, m]
   * and [m, r] such that there is a valid total order for each sub-interval.
   */
  Time findSplitPoint(Interval itv, const std::vector<ElemId>& elems);

  /**
   * Attempts to construct a valid total order for the operations in
   * ElemsIn(itv) by splitting the interval into [l, m] and [m, r] for given m.
   * @return List of operation IDs in a valid total order
   */
  std::list<OpId> consBySplit(Interval itv, const std::vector<ElemId>& elems,
                              Time m);

  /**
   * Attaches an element to the ends of a total order.
   */
  void attachToEnds(std::list<OpId>& order, Interval itv, ElemId elem);

  /** Attaches an operation to an end of a total order. */
  void attachToEnd(std::list<OpId>& order, Interval itv, const StackOp& op);

  /** Constructs a total order for the operations of a single element. */
  std::list<OpId> consElemOps(ElemId elem);

  /**
   * Attempts to construct a valid total order for the operations in
   * ElemsIn(itv) by covering the interval with a single element.
   * @return List of operation IDs in a valid total order
   */
  std::optional<std::list<OpId>> consByCover(Interval itv,
                                             const std::vector<ElemId>& elems);

  /**
   * Finds an element that can cover the interval itv.
   * @return Pair of (element, peek times)
   */
  std::optional<std::pair<ElemId, std::vector<Time>>>
  findCoverDetails(Interval itv, const std::vector<ElemId>& elems);

  class PeekPlacer;

  /**
   * Constructs a valid total order for operations in ElemsIn(itv) using a
   * covering element. Assumes construction is possible (verified by prior
   * checks). Uses the provided covering element and its peek times to construct
   * the order. The covering element's push/pop operations will span the
   * interval, with peeks executed at the specified times.
   *
   * @param itv Interval to construct order for
   * @param elems Elements whose operations need ordering
   * @param covering_elem Element whose operations span the interval
   * @param peek_times Times at which peek operations should be executed
   * @return List of operation IDs in a valid total order
   */
  std::list<OpId> consByCover(Interval itv, const std::vector<ElemId>& elems,
                              ElemId covering_elem,
                              const std::vector<Time>& peek_times);

  /**
   * Appends peek operations to the order.
   * Peeks are added if they can be executed earliest at peek_time.
   */
  void appendPeeks(std::list<OpId>& order, ElemId covering_elem,
                   Time last_peek_time, Time peek_time);

  /**
   * Extracts elements from the input vector that can be fully executed within
   * the given interval, constructs a valid total order for their operations,
   * and returns the remaining elements.
   *
   * @param elems Vector of elements to process
   * @param itv Interval in which to execute operations
   * @return A pair containing:
   *         1. Vector of elements that couldn't be fully executed in the
   * interval
   *         2. List of operation IDs representing a valid total order for the
   * extracted elements
   */
  std::pair<std::list<OpId>, std::vector<ElemId>>
  extractOrderableElements(std::vector<ElemId> elems, Interval itv);

  /**
   * Checks if an element must be on the stack at time t.
   * @return [push.upper < t && pop.lower > t]
   */
  bool isInStack(ElemId elem, Time t);

  /**
   * Returns the list of all elements that are "In" the interval itv.
   * An element is "In" if it has at least one operation that must be executed
   * in itv.
   * @return ElemsIn(itv) where {e | e in V <==> In(e, itv)}
   */
  std::vector<ElemId> elemsIn(Interval itv);

  /**
   * Checks if an element must be "In" the interval itv.
   * @return [isIn(op, itv) for op in ops(elem)]
   */
  bool isIn(Interval itv, ElemId elem);
};

/*
 * Helper class for placing peek operations.
 */
class SimpleDPNoEmpty::PeekPlacer {
public:
  struct Params {
    ElemOps covering_ops;
    Interval itv;
    std::function<bool(Interval)> can_cons_in;
    std::function<bool(Time)> is_any_on_stack;
  };
  PeekPlacer(Params params);
  std::optional<std::vector<Time>> getPeekTimes();

private:
  ElemOpsMap elem_ops;
  Params params;
  std::unordered_map<Time, Time> next_peek_time{};

  /** Recursively attempts to place peek operations starting from time t. */
  bool placePeeks(Time t);

  /** Extracts the peek times from the memo table. */
  std::vector<Time> extractPeekTimes();

  /**
   * Returns the lower and upper bounds for the next peek time.
   * @return (lower, upper)
   */
  std::pair<Time, Time> getNextPeekBounds(Time t);
};

[[nodiscard]] std::optional<std::list<OpId>>
SimpleDPNoEmpty::constructTotalOrder(const ElemOpsMap& ops) {
  elem_ops = ops;

  tightenBounds(elem_ops);
  if (!areItvsValid(elem_ops)) {
    return std::nullopt;
  }

  initializeTimeBounds();

  Interval full_interval{time_lower_bound - 1, time_upper_bound + 1};
  assert(elemsIn(full_interval).size() == elem_ops.size());

  return consIn(full_interval);
}

void SimpleDPNoEmpty::initializeTimeBounds() {
  time_lower_bound = std::numeric_limits<Time>::max();
  time_upper_bound = std::numeric_limits<Time>::min();
  for (const auto& [_, ops] : elem_ops) {
    time_lower_bound = std::min(time_lower_bound, ops.push.lower());
    time_upper_bound = std::max(time_upper_bound, ops.pop.upper());
  }
}

std::optional<std::list<OpId>> SimpleDPNoEmpty::consIn(Interval itv) {
  if (memo_cons_total_order_in.count(itv) > 0) {
    return memo_cons_total_order_in[itv];
  }

  auto elems = elemsIn(itv);
  if (elems.empty()) {
    return memo_cons_total_order_in[itv] = std::list<OpId>();
  }

  if (!canFit(itv, elems)) {
    return memo_cons_total_order_in[itv] = std::nullopt;
  }

  auto split_result = consBySplit(itv, elems);
  if (split_result) {
    return memo_cons_total_order_in[itv] = std::move(*split_result);
  }

  auto cover_result = consByCover(itv, elems);
  if (cover_result) {
    return memo_cons_total_order_in[itv] = std::move(*cover_result);
  }

  return memo_cons_total_order_in[itv] = std::nullopt;
}

bool SimpleDPNoEmpty::canFit(Interval itv, const std::vector<ElemId>& elems) {
  return std::all_of(elems.cbegin(), elems.cend(),
                     [this, itv](ElemId elem) { return canFit(itv, elem); });
}

bool SimpleDPNoEmpty::canFit(Interval itv, ElemId elem) {
  return itv.l <= elem_ops.at(elem).push.upper() &&
         itv.r >= elem_ops.at(elem).pop.lower();
}

std::optional<std::list<OpId>>
SimpleDPNoEmpty::consBySplit(Interval itv, const std::vector<ElemId>& elems) {
  Time m = findSplitPoint(itv, elems);
  if (m == -1) {
    return std::nullopt;
  }

  return consBySplit(itv, elems, m);
}

Time SimpleDPNoEmpty::findSplitPoint(Interval itv,
                                     const std::vector<ElemId>& elems) {
  for (Time m = itv.l + 1; m < itv.r; ++m) {
    bool allElemsOffStack =
        std::all_of(elems.cbegin(), elems.cend(),
                    [this, m](ElemId elem) { return !isInStack(elem, m); });

    if (allElemsOffStack && consIn({itv.l, m}) && consIn({m, itv.r})) {
      return m;
    }
  }
  return -1;
}

std::list<OpId> SimpleDPNoEmpty::consBySplit(Interval itv,
                                             const std::vector<ElemId>& elems,
                                             Time m) {
  const auto l = itv.l;
  const auto r = itv.r;

  auto left_order = consIn({l, m}).value();
  auto right_order = consIn({m, r}).value();

  auto remaining_elems =
      elems | std::views::filter([this, l, m, r](ElemId elem) {
        return !isIn({l, m}, elem) && !isIn({m, r}, elem);
      });

  for (ElemId elem : remaining_elems) {
    assert(canFit({l, m}, elem) || canFit({m, r}, elem));
    if (canFit({l, m}, elem)) {
      attachToEnds(left_order, {l, m}, elem);
    } else {
      attachToEnds(right_order, {m, r}, elem);
    }
  }

  left_order.splice(left_order.end(), right_order);
  return left_order;
}

void SimpleDPNoEmpty::attachToEnds(std::list<OpId>& order, Interval itv,
                                   ElemId elem) {
  if (canExec(elem_ops.at(elem).push, itv.r)) {
    order.splice(order.end(), consElemOps(elem));
  } else if (canExec(elem_ops.at(elem).pop, itv.l)) {
    order.splice(order.begin(), consElemOps(elem));
  } else {
    for (const auto& peek : elem_ops.at(elem).peeks) {
      attachToEnd(order, itv, peek);
    }
    order.push_front(elem_ops.at(elem).push.id);
    order.push_back(elem_ops.at(elem).pop.id);
  }
}

void SimpleDPNoEmpty::attachToEnd(std::list<OpId>& order, Interval itv,
                                  const StackOp& op) {
  if (canExec(op, itv.r)) {
    order.push_back(op.id);
  } else if (canExec(op, itv.l)) {
    order.push_front(op.id);
  } else {
    throw std::runtime_error("Operation assignable to ends of interval");
  }
}

std::list<OpId> SimpleDPNoEmpty::consElemOps(ElemId elem) {
  std::list<OpId> order;
  order.push_back(elem_ops.at(elem).push.id);
  for (const auto& peek : elem_ops.at(elem).peeks) {
    order.push_back(peek.id);
  }
  order.push_back(elem_ops.at(elem).pop.id);
  return order;
}

std::optional<std::list<OpId>>
SimpleDPNoEmpty::consByCover(Interval itv, const std::vector<ElemId>& elems) {
  auto cover_details = findCoverDetails(itv, elems);
  if (!cover_details) {
    return std::nullopt;
  }

  auto [covering_element, peek_times] = cover_details.value();
  return consByCover(itv, elems, covering_element, peek_times);
}

std::optional<std::pair<ElemId, std::vector<Time>>>
SimpleDPNoEmpty::findCoverDetails(Interval itv,
                                  const std::vector<ElemId>& elems) {
  for (ElemId covering_elem : elems) {
    auto covered_elems =
        elems | std::views::filter([covering_elem](ElemId elem) {
          return elem != covering_elem;
        });

    auto peek_times =
        PeekPlacer(
            {.covering_ops = elem_ops.at(covering_elem),
             .itv = itv,
             .can_cons_in =
                 [this](Interval itv) { return consIn(itv).has_value(); },
             .is_any_on_stack =
                 [this, &covered_elems](Time t) {
                   return std::ranges::any_of(
                       covered_elems,
                       [this, t](ElemId elem) { return isInStack(elem, t); });
                 }})

            .getPeekTimes();

    if (peek_times) {
      return std::make_pair(covering_elem, peek_times.value());
    }
  }
  return std::nullopt;
}

SimpleDPNoEmpty::PeekPlacer::PeekPlacer(Params params) : params(params) {}

std::optional<std::vector<Time>> SimpleDPNoEmpty::PeekPlacer::getPeekTimes() {
  if (!canExec(params.covering_ops.push, params.itv.l) ||
      !canExec(params.covering_ops.pop, params.itv.r)) {
    return std::nullopt;
  }

  bool placed = placePeeks(params.itv.l);
  if (!placed) {
    return std::nullopt;
  }

  return extractPeekTimes();
}

bool SimpleDPNoEmpty::PeekPlacer::placePeeks(Time t) {
  if (t >= params.itv.r) {
    return true;
  }

  if (next_peek_time.count(t) > 0) {
    return true;
  }

  auto [lower_bound, upper_bound] = getNextPeekBounds(t);

  for (Time next_t = upper_bound; next_t >= lower_bound; --next_t) {
    if (params.can_cons_in({t, next_t}) && !params.is_any_on_stack(next_t) &&
        placePeeks(next_t)) {
      next_peek_time[t] = next_t;
      return true;
    }
  }

  return false;
}

std::vector<Time> SimpleDPNoEmpty::PeekPlacer::extractPeekTimes() {
  std::vector<Time> peek_times;
  for (Time t = params.itv.l; t < params.itv.r; t = next_peek_time[t]) {
    peek_times.push_back(t);
  }
  peek_times.push_back(params.itv.r);
  return peek_times;
}

std::pair<Time, Time> SimpleDPNoEmpty::PeekPlacer::getNextPeekBounds(Time t) {
  Time lower_bound = params.itv.r;
  Time upper_bound = params.itv.r;
  for (const auto& peek : params.covering_ops.peeks) {
    if (peek.lower() > t) {
      lower_bound = std::min(lower_bound, peek.lower());
      upper_bound = std::min(upper_bound, peek.upper());
    }
  }
  return {lower_bound, upper_bound};
}

std::list<OpId>
SimpleDPNoEmpty::consByCover(Interval itv, const std::vector<ElemId>& elems,
                             ElemId covering_elem,
                             const std::vector<Time>& peek_times) {
  std::vector<ElemId> remaining_elems;
  std::ranges::copy(elems | std::views::filter([covering_elem](ElemId elem) {
                      return elem != covering_elem;
                    }),
                    std::back_inserter(remaining_elems));

  std::list<OpId> order = {elem_ops.at(covering_elem).push.id};

  Time last_peek_time = -1;
  for (Time peek_time : peek_times) {
    if (peek_time != itv.l) {
      auto [sub_order, new_remaining_elems] = extractOrderableElements(
          remaining_elems, {last_peek_time, peek_time});

      remaining_elems = std::move(new_remaining_elems);
      order.splice(order.end(), sub_order);
    }

    appendPeeks(order, covering_elem, last_peek_time, peek_time);
    last_peek_time = peek_time;
  }

  order.push_back(elem_ops.at(covering_elem).pop.id);
  return order;
}

void SimpleDPNoEmpty::appendPeeks(std::list<OpId>& order, ElemId covering_elem,
                                  Time last_peek_time, Time peek_time) {
  auto peeks_to_add =
      elem_ops.at(covering_elem).peeks |
      std::views::filter([last_peek_time, peek_time](const auto& peek) {
        return canExec(peek, peek_time) && !canExec(peek, last_peek_time);
      });

  auto ids = peeks_to_add | std::views::transform(&StackOp::id);

  std::ranges::copy(ids, std::back_inserter(order));
}

std::pair<std::list<OpId>, std::vector<ElemId>>
SimpleDPNoEmpty::extractOrderableElements(std::vector<ElemId> elems,
                                          Interval itv) {
  std::list<OpId> sub_order = consIn(itv).value(); // contains all elems In(itv)
  std::vector<ElemId> remaining_elems;

  for (ElemId elem : elems) {
    if (!canFit(itv, elem)) {
      remaining_elems.push_back(elem);
    } else if (!isIn(itv, elem)) {
      attachToEnds(sub_order, itv, elem);
    }
  }
  return {sub_order, remaining_elems};
}

bool SimpleDPNoEmpty::isInStack(ElemId elem, Time t) {
  return elem_ops.at(elem).push.upper() < t &&
         t < elem_ops.at(elem).pop.lower();
}

std::vector<ElemId> SimpleDPNoEmpty::elemsIn(Interval itv) {
  std::vector<ElemId> elems;
  for (auto [elem, _] : elem_ops) {
    if (isIn(itv, elem)) {
      elems.push_back(elem);
    }
  }
  return elems;
}

bool SimpleDPNoEmpty::isIn(Interval itv, ElemId elem) {
  return std::ranges::any_of(
             elem_ops.at(elem).peeks,
             [itv](const auto& op) { return isOpInItv(itv, op); }) ||
         isOpInItv(itv, elem_ops.at(elem).push) ||
         isOpInItv(itv, elem_ops.at(elem).pop);
}

} // namespace

SimpleDP::SimpleDP(const std::vector<StackOp>& ops) : HistoryConstructor(ops) {}

std::optional<std::list<OpId>> SimpleDP::constructTotalOrder() {
  return FilterEmpty<SimpleDPNoEmpty>().constructTotalOrder(ops);
}

std::optional<TotalHistory> SimpleDP::constructHistory() {
  return SimpleDP::constructTotalOrder().transform(
      [this](const auto& order) { return convertOrderToHistory(order, ops); });
}

} // namespace stack_cons
