#pragma once

#include "common/testing.hpp"
#include "cons.hpp"
#include "generator.hpp"
#include "printer.hpp"
#include <algorithm>
#include <cassert>
#include <functional>
#include <iostream>
#include <vector>

namespace stack_cons {

const common::testing::TestConfigParamList DEFAULT_TEST_CONFIG = {
    .trials_per_config = 100,
    .max_ops_values = {5, 10, 15, 20, 25, 50, 100},
    .variance_factors = {2.0, 5.0, 10.0, 50.0},
    .permutation_factors = {0, 1, 2, 5, 10}};

using Solver = common::testing::Solver<StackOp>;

std::optional<std::vector<StackOp>> findCounterexample(
    const Solver& solver,
    const common::generator::InputConfig& config = {.max_ops = 10,
                                                    .variance_factor = 2,
                                                    .permutation_factor = 0},
    int num_trials = 1000);

void benchmarkConfigurations(
    const Solver& solver,
    const common::testing::TestConfigParamList& config = DEFAULT_TEST_CONFIG);

std::optional<std::vector<StackOp>> testSolverTime(
    const Solver& solver,
    const common::generator::InputConfig& config = {.max_ops = 10,
                                                    .variance_factor = 2,
                                                    .permutation_factor = 0},
    std::chrono::milliseconds time_threshold = std::chrono::milliseconds(1000),
    int num_trials = 100);

std::optional<
    std::tuple<generator::InputConfig, std::vector<StackOp>,
               std::optional<TotalHistory>, std::optional<TotalHistory>>>
compareSolvers(
    const Solver& solver1, const Solver& solver2,
    const common::testing::TestConfigParamList& config = DEFAULT_TEST_CONFIG);

} // namespace stack_cons
