/**
 * @file generator.cpp
 * @brief Implementation of the TotalOrderStackGenerator class
 *
 * This generator creates test cases for stack linearizability algorithms by
 * generating sequences of operations with timing constraints.
 */

#include "generator.hpp"
#include "common/generator_impl.hpp"
#include "common/sampling.hpp"
#include <cassert>
#include <algorithm>
#include <numeric>
#include <stdexcept>

namespace stack_cons::generator {

class Tracker {
public:
  using OpType = stack_cons::OpType;

  Tracker() = default;

  [[nodiscard]] std::pair<ElemId, OpId> trackOperation(OpType op_type) {
    OpId op_id = next_id_++;

    switch (op_type) {
    case OpType::PUSH: {
      elements_.push_back(next_element_);
      return {next_element_++, op_id};
    }
    case OpType::POP: {
      if (elements_.empty()) {
        throw std::runtime_error("Stack underflow during generation");
      }
      ElemId element = elements_.back();
      elements_.pop_back();
      return {element, op_id};
    }
    case OpType::PEEK: {
      if (elements_.empty()) {
        throw std::runtime_error("Cannot peek empty stack");
      }
      return {elements_.back(), op_id};
    }
    case OpType::EMPTY: {
      return {INVALID_OP_ID, op_id}; // Element ID doesn't matter for EMPTY
    }
    default:
      throw std::runtime_error("Unknown operation type");
    }
  }

private:
  OpId next_id_ = 0;
  ElemId next_element_ = 0;
  std::vector<ElemId> elements_;
};

class OpTypeClassifier {
public:
  using OpType = stack_cons::OpType;

  static constexpr int NUM_TYPES = 4;

  [[nodiscard]] static common::generator::ContainerEffect
  getContainerEffect(OpType op_type) {
    switch (op_type) {
    case OpType::PUSH:
      return ContainerEffect::ADD;
    case OpType::POP:
      return ContainerEffect::REMOVE;
    case OpType::PEEK:
      return ContainerEffect::PEEK;
    case OpType::EMPTY:
      return ContainerEffect::EMPTY;
    }
    assert(false);
  }
};

[[nodiscard]] std::vector<StackOp> generateStackOps(const InputConfig& config,
                                                    URNG& rng) {
  return common::generator::generateOps<StackOp, OpTypeClassifier, Tracker>(
      config, rng);
}

} // namespace stack_cons::generator
