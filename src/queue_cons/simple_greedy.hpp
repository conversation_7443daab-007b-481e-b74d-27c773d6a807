#pragma once

#include "common/interval.hpp"
#include "cons.hpp"
#include <vector>
#include <list>
#include <optional>

namespace queue_cons {

class SimpleGreedy : public HistoryConstructor {
public:
  explicit SimpleGreedy(const std::vector<QueueOp>& ops);

  std::optional<std::list<OpId>> constructTotalOrder() override;
  std::optional<TotalHistory> constructHistory() override;
};

} // namespace queue_cons