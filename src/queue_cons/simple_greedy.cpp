#include "simple_greedy.hpp"
#include "filter_empty.hpp"
#include <algorithm>
#include <cassert>
#include <cstddef>
#include <iterator>
#include <limits>
#include <ranges>
#include <set>

namespace queue_cons {

namespace {
class SimpleGreedyNoEmpty {
public:
  explicit SimpleGreedyNoEmpty() = default;

  [[nodiscard]] std::optional<std::list<OpId>>
  constructTotalOrder(const ElemOpsMap& ops);

private:
  ElemOpsMap elem_ops;
};

struct ElemBounds {
  Time front_lower;
  Time front_upper;
  Time back_lower;
  Time back_upper;
};

ElemBounds getBounds(const ElemOps& ops) {
  ElemBounds bounds{ops.dequeue.lower(), ops.dequeue.upper(),
                    ops.enqueue.lower(), ops.enqueue.upper()};

  for (const auto& peek : ops.peeks_front) {
    bounds.front_lower = std::max(bounds.front_lower, peek.lower());
    bounds.front_upper = std::min(bounds.front_upper, peek.upper());
  }

  for (const auto& peek : ops.peeks_back) {
    bounds.back_lower = std::max(bounds.back_lower, peek.lower());
    bounds.back_upper = std::min(bounds.back_upper, peek.upper());
  }

  return bounds;
}

[[nodiscard]] std::unordered_map<ElemId, ElemBounds>
buildElemBounds(const ElemOpsMap& elem_ops) {
  std::unordered_map<ElemId, ElemBounds> result;
  for (const auto& [elem_id, ops] : elem_ops) {
    result[elem_id] = getBounds(ops);
  }
  return result;
}

class ElemOrderBuilder {
public:
  explicit ElemOrderBuilder(
      const std::unordered_map<ElemId, ElemBounds>& elem_bounds);

  [[nodiscard]] std::optional<std::vector<ElemId>> build();

private:
  struct Bound {
    Time lower;
    Time upper;
  };

  std::unordered_map<ElemId, Bound> front_bounds{};
  std::unordered_map<ElemId, Bound> back_bounds{};

  std::set<std::pair<Time, ElemId>> front_uppers{};
  std::set<std::pair<Time, ElemId>> back_uppers{};
  std::set<std::pair<Time, ElemId>> front_lowers{};
  std::set<std::pair<Time, ElemId>> back_lowers{};
  std::unordered_map<ElemId, int> in_lower_count{};
  std::vector<ElemId> ready_elems{};

  void reduceLowerCount(ElemId elem_id);
  void makeReady(std::set<std::pair<Time, ElemId>>& uppers,
                 std::set<std::pair<Time, ElemId>>& lowers,
                 std::unordered_map<ElemId, Bound>& bounds);
  void removeFromSets(ElemId elem_id);
};

ElemOrderBuilder::ElemOrderBuilder(
    const std::unordered_map<ElemId, ElemBounds>& elem_bounds) {
  for (const auto& [elem_id, bounds] : elem_bounds) {
    front_bounds[elem_id] = {bounds.front_lower, bounds.front_upper};
    back_bounds[elem_id] = {bounds.back_lower, bounds.back_upper};

    front_uppers.insert({bounds.front_upper, elem_id});
    back_uppers.insert({bounds.back_upper, elem_id});
    front_lowers.insert({bounds.front_lower, elem_id});
    back_lowers.insert({bounds.back_lower, elem_id});

    in_lower_count[elem_id] = 2;
  }
}

void ElemOrderBuilder::reduceLowerCount(ElemId elem_id) {
  in_lower_count[elem_id]--;
  if (in_lower_count[elem_id] == 0) {
    ready_elems.push_back(elem_id);
  }
}

void ElemOrderBuilder::makeReady(std::set<std::pair<Time, ElemId>>& uppers,
                                 std::set<std::pair<Time, ElemId>>& lowers,
                                 std::unordered_map<ElemId, Bound>& bounds) {
  if (uppers.empty()) {
    return;
  }

  while (!lowers.empty() && lowers.cbegin()->first <= uppers.cbegin()->first) {
    reduceLowerCount(lowers.cbegin()->second);
    lowers.erase(lowers.cbegin());
  }

  ElemId cand_id = uppers.cbegin()->second;
  if (auto it = lowers.find({bounds[cand_id].lower, cand_id});
      it != lowers.end()) {
    if (uppers.size() == 1 || it->first <= next(uppers.cbegin())->first) {
      reduceLowerCount(cand_id);
      lowers.erase(it);
    }
  }
}

void ElemOrderBuilder::removeFromSets(ElemId elem_id) {
  auto front_upper_it =
      front_uppers.find({front_bounds[elem_id].upper, elem_id});
  if (front_upper_it != front_uppers.end()) {
    front_uppers.erase(front_upper_it);
  }

  auto back_upper_it = back_uppers.find({back_bounds[elem_id].upper, elem_id});
  if (back_upper_it != back_uppers.end()) {
    back_uppers.erase(back_upper_it);
  }

  auto front_lower_it =
      front_lowers.find({front_bounds[elem_id].lower, elem_id});
  if (front_lower_it != front_lowers.end()) {
    front_lowers.erase(front_lower_it);
  }

  auto back_lower_it = back_lowers.find({back_bounds[elem_id].lower, elem_id});
  if (back_lower_it != back_lowers.end()) {
    back_lowers.erase(back_lower_it);
  }
}

std::optional<std::vector<ElemId>> ElemOrderBuilder::build() {
  std::vector<ElemId> result;
  result.reserve(front_bounds.size());
  for (size_t _ = 0; _ < front_bounds.size(); ++_) {
    makeReady(front_uppers, front_lowers, front_bounds);
    makeReady(back_uppers, back_lowers, back_bounds);

    if (ready_elems.empty()) {
      return std::nullopt;
    }
    removeFromSets(ready_elems.back());
    result.push_back(ready_elems.back());
    ready_elems.pop_back();
  }

  return result;
}

struct TimeAndOpId {
  Time time;
  OpId op_id;
};
using SideHistory = std::vector<TimeAndOpId>;

std::pair<SideHistory, SideHistory>
buildHistory(const std::vector<ElemId>& elem_order,
             const ElemOpsMap& elem_ops) {
  SideHistory back_history;
  SideHistory front_history;
  Time back_time = std::numeric_limits<Time>::min();
  Time front_time = std::numeric_limits<Time>::min();

  for (ElemId elem_id : elem_order) {
    back_time = std::max(back_time, elem_ops.at(elem_id).enqueue.lower());
    front_time = std::max(front_time, back_time);

    assert(back_time <= elem_ops.at(elem_id).enqueue.upper());
    back_history.push_back({back_time, elem_ops.at(elem_id).enqueue.id});

    auto peeks_back = elem_ops.at(elem_id).peeks_back;
    std::ranges::sort(peeks_back, [](const auto& a, const auto& b) {
      return a.lower() < b.lower();
    });
    for (const auto& peek : peeks_back) {
      back_time = std::max(back_time, peek.lower());
      assert(back_time <= peek.upper());
      back_history.push_back({back_time, peek.id});
    }

    auto peeks_front = elem_ops.at(elem_id).peeks_front;
    std::ranges::sort(peeks_front, [](const auto& a, const auto& b) {
      return a.lower() < b.lower();
    });
    for (const auto& peek : peeks_front) {
      front_time = std::max(front_time, peek.lower());
      assert(front_time <= peek.upper());
      front_history.push_back({front_time, peek.id});
    }

    front_time = std::max(front_time, elem_ops.at(elem_id).dequeue.lower());
    assert(front_time <= elem_ops.at(elem_id).dequeue.upper());
    front_history.push_back({front_time, elem_ops.at(elem_id).dequeue.id});
  }

  return {back_history, front_history};
}

std::list<OpId> buildOrder(const SideHistory& back_history,
                           const SideHistory& front_history) {
  std::list<OpId> order;
  auto back_it = back_history.begin();
  auto front_it = front_history.begin();

  while (back_it != back_history.end() || front_it != front_history.end()) {
    if (back_it == back_history.end()) {
      order.push_back(front_it->op_id);
      ++front_it;
    } else if (front_it == front_history.end()) {
      order.push_back(back_it->op_id);
      ++back_it;
    } else if (back_it->time <= front_it->time) {
      order.push_back(back_it->op_id);
      ++back_it;
    } else {
      order.push_back(front_it->op_id);
      ++front_it;
    }
  }
  return order;
}

std::list<OpId> buildTotalOrder(const std::vector<ElemId>& elem_order,
                                const ElemOpsMap& elem_ops) {
  auto [backHistory, frontHistory] = buildHistory(elem_order, elem_ops);
  return buildOrder(backHistory, frontHistory);
}

[[nodiscard]] std::optional<std::list<OpId>>
SimpleGreedyNoEmpty::constructTotalOrder(const ElemOpsMap& ops) {
  elem_ops = ops;

  tightenBounds(elem_ops);
  if (!areItvsValid(elem_ops)) {
    return std::nullopt;
  }

  std::unordered_map<ElemId, ElemBounds> elem_bounds =
      buildElemBounds(elem_ops);

  std::optional<std::vector<ElemId>> elem_order =
      ElemOrderBuilder(elem_bounds).build();

  if (elem_order) {
    return buildTotalOrder(elem_order.value(), elem_ops);
  }

  return std::nullopt;
}

} // namespace

SimpleGreedy::SimpleGreedy(const std::vector<QueueOp>& ops)
    : HistoryConstructor(ops) {}

std::optional<std::list<OpId>> SimpleGreedy::constructTotalOrder() {
  return FilterEmpty<SimpleGreedyNoEmpty>().constructTotalOrder(ops);
}

std::optional<TotalHistory> SimpleGreedy::constructHistory() {
  return SimpleGreedy::constructTotalOrder().transform(
      [this](const auto& order) { return convertOrderToHistory(order, ops); });
}

} // namespace queue_cons
