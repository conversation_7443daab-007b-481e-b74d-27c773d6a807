#pragma once

#include "common/testing.hpp"
#include "common/generator.hpp"
#include "cons.hpp"
#include <optional>
#include <vector>
#include <chrono>

namespace queue_cons {

using Solver = common::testing::Solver<QueueOp>;

const common::testing::TestConfigParamList DEFAULT_TEST_CONFIG = {
    .trials_per_config = 100,
    .max_ops_values = {5, 10, 15, 20, 25, 50, 100},
    .variance_factors = {2.0, 5.0, 10.0, 50.0},
    .permutation_factors = {0, 1, 2, 5, 10}
};

std::optional<std::vector<QueueOp>> findCounterexample(
    const Solver& solver,
    const common::generator::InputConfig& config = {.max_ops = 10,
                                                  .variance_factor = 2,
                                                  .permutation_factor = 0},
    int num_trials = 1000);

void benchmarkConfigurations(
    const Solver& solver,
    const common::testing::TestConfigParamList& config = DEFAULT_TEST_CONFIG);

std::optional<std::vector<QueueOp>> testSolverTime(
    const Solver& solver,
    const common::generator::InputConfig& config = {.max_ops = 10,
                                                  .variance_factor = 2,
                                                  .permutation_factor = 0},
    std::chrono::milliseconds time_threshold = std::chrono::milliseconds(1000),
    int num_trials = 100);

std::optional<
    std::tuple<generator::InputConfig, std::vector<QueueOp>,
               std::optional<TotalHistory>, std::optional<TotalHistory>>>
compareSolvers(
    const Solver& solver1,
    const Solver& solver2,
    const common::testing::TestConfigParamList& config = DEFAULT_TEST_CONFIG);

} // namespace queue_cons