#include "cons.hpp"
#include <algorithm>

namespace queue_cons {

std::unordered_map<ElemId, ElemOps>
filterOperationsByElement(const std::vector<QueueOp>& operations) {
  std::unordered_map<ElemId, std::optional<QueueOp>> enqueue_ops;
  std::unordered_map<ElemId, std::optional<QueueOp>> dequeue_ops;
  std::unordered_map<ElemId, std::vector<QueueOp>> peek_front_ops;
  std::unordered_map<ElemId, std::vector<QueueOp>> peek_back_ops;

  for (const auto& op : operations) {
    switch (op.type) {
    case OpType::ENQUEUE:
      enqueue_ops[op.element] = op;
      break;
    case OpType::DEQUEUE:
      dequeue_ops[op.element] = op;
      break;
    case OpType::PEEK_FRONT:
      peek_front_ops[op.element].push_back(op);
      break;
    case OpType::PEEK_BACK:
      peek_back_ops[op.element].push_back(op);
      break;
    case OpType::EMPTY:
      assert(false);
    }
  }

  std::unordered_map<ElemId, ElemOps> result;
  for (const auto& [elem_id, enqueue_op] : enqueue_ops) {
    if (!dequeue_ops.contains(elem_id) || !dequeue_ops[elem_id]) {
      throw std::runtime_error(
          "Invalid operations: missing enqueue or dequeue for element " +
          std::to_string(elem_id));
    }

    result.emplace(elem_id,
                   ElemOps{enqueue_op.value(), dequeue_ops[elem_id].value(),
                           peek_front_ops[elem_id], peek_back_ops[elem_id]});
  }

  return result;
}

void tightenBounds(ElemOps& element_ops) {
    auto& enq = element_ops.enqueue;
    auto& deq = element_ops.dequeue;
    auto& peeks_front = element_ops.peeks_front;
    auto& peeks_back = element_ops.peeks_back;

    // All operations must happen after enqueue
    for (auto& peek : peeks_front) {
        raiseLowerBound(peek.itv, enq.itv);
    }
    for (auto& peek : peeks_back) {
        raiseLowerBound(peek.itv, enq.itv);
    }
    raiseLowerBound(deq.itv, enq.itv);

    // Enqueue must happen before all operations
    for (auto& peek : peeks_front) {
        reduceUpperBound(enq.itv, peek.itv);
    }
    for (auto& peek : peeks_back) {
        reduceUpperBound(enq.itv, peek.itv);
    }
    reduceUpperBound(enq.itv, deq.itv);

    // All operations must happen before dequeue
    for (auto& peek : peeks_front) {
        reduceUpperBound(peek.itv, deq.itv);
    }
    for (auto& peek : peeks_back) {
        reduceUpperBound(peek.itv, deq.itv);
    }

    // Dequeue must happen after all operations
    for (auto& peek : peeks_front) {
        raiseLowerBound(deq.itv, peek.itv);
    }
    for (auto& peek : peeks_back) {
        raiseLowerBound(deq.itv, peek.itv);
    }
}

void tightenBounds(std::unordered_map<ElemId, ElemOps>& element_ops) {
    for (auto& [_, ops] : element_ops) {
        tightenBounds(ops);
    }
}

[[nodiscard]] bool areItvsValid(const ElemOps& element_ops) {
    auto& enq = element_ops.enqueue;
    auto& deq = element_ops.dequeue;
    auto& peeks_front = element_ops.peeks_front;
    auto& peeks_back = element_ops.peeks_back;

    return isValid(enq.itv) && isValid(deq.itv) &&
           std::all_of(peeks_front.cbegin(), peeks_front.cend(),
                      [](const auto& op) { return isValid(op.itv); }) &&
           std::all_of(peeks_back.cbegin(), peeks_back.cend(),
                      [](const auto& op) { return isValid(op.itv); });
}

[[nodiscard]] bool areItvsValid(const std::unordered_map<ElemId, ElemOps>& element_ops) {
    return std::all_of(element_ops.cbegin(), element_ops.cend(),
                      [](const auto& pair) { return areItvsValid(pair.second); });
}

} // namespace queue_cons
