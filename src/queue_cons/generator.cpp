#include "generator.hpp"
#include "common/generator_impl.hpp"
#include "common/sampling.hpp"
#include <algorithm>
#include <numeric>
#include <stdexcept>

namespace queue_cons::generator {

class Tracker {
public:
  using OpType = queue_cons::OpType;

  [[nodiscard]] std::pair<ElemId, OpId> trackOperation(OpType op_type) {
    OpId op_id = next_id_++;

    switch (op_type) {
    case OpType::ENQUEUE: {
      elements_.push_back(next_element_);
      return {next_element_++, op_id};
    }
    case OpType::DEQUEUE: {
      if (elements_.empty()) {
        throw std::runtime_error("Queue underflow during generation");
      }
      ElemId element = elements_.front();
      elements_.pop_front();
      return {element, op_id};
    }
    case OpType::PEEK_FRONT: {
      if (elements_.empty()) {
        throw std::runtime_error("Cannot peek empty queue");
      }
      return {elements_.front(), op_id};
    }
    case OpType::PEEK_BACK: {
      if (elements_.empty()) {
        throw std::runtime_error("Cannot peek empty queue");
      }
      return {elements_.back(), op_id};
    }
    case OpType::EMPTY: {
      return {INVALID_OP_ID, op_id}; // Element ID doesn't matter for EMPTY
    }
    default:
      throw std::runtime_error("Unknown operation type");
    }
  }

private:
  OpId next_id_ = 0;
  ElemId next_element_ = 0;
  std::deque<ElemId> elements_;
};

class OpTypeClassifier {
public:
  using OpType = queue_cons::OpType;

  static constexpr int NUM_TYPES = 5;

  [[nodiscard]] static common::generator::ContainerEffect
  getContainerEffect(OpType op_type) {
    switch (op_type) {
    case OpType::ENQUEUE:
      return ContainerEffect::ADD;
    case OpType::DEQUEUE:
      return ContainerEffect::REMOVE;
    case OpType::PEEK_FRONT:
    case OpType::PEEK_BACK:
      return ContainerEffect::PEEK;
    case OpType::EMPTY:
      return ContainerEffect::EMPTY;
    }
    assert(false);
    return ContainerEffect::PEEK;
  }
};

[[nodiscard]] std::vector<QueueOp>
generateQueueOps(const InputConfig& config, URNG& rng) {
  return common::generator::generateOps<QueueOp, OpTypeClassifier, Tracker>(
      config, rng);
}

} // namespace queue_cons::generator
