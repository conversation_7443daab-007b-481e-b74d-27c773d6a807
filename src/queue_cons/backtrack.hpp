#pragma once

#include "common/backtrack_impl.hpp"
#include "cons.hpp"
#include <deque>
#include <unordered_set>

namespace queue_cons {

class QueueSimulator {
public:
    using Op = QueueOp;

    bool canAddOperation(const QueueOp& op) {
        switch (op.type) {
        case OpType::ENQUEUE:
            return true;
        case OpType::DEQUEUE:
            return !queue_state.empty() && queue_state.front() == op.element;
        case OpType::PEEK_FRONT:
            return !queue_state.empty() && queue_state.front() == op.element;
        case OpType::PEEK_BACK:
            return !queue_state.empty() && queue_state.back() == op.element;
        case OpType::EMPTY:
            return queue_state.empty();
        }
        return false;
    }

    void addOperation(const QueueOp& op) {
        if (op.type == OpType::ENQUEUE) {
            queue_state.push_back(op.element);
        } else if (op.type == OpType::DEQUEUE) {
            queue_state.pop_front();
        }
        // EMPTY, PEEK_FRONT, and PEEK_BACK don't modify state
    }

    void removeOperation(const QueueOp& op) {
        if (op.type == OpType::ENQUEUE) {
            queue_state.pop_back();
        } else if (op.type == OpType::DEQUEUE) {
            queue_state.push_front(op.element);
        }
        // EMPTY, PEEK_FRONT, and PEEK_BACK don't modify state
    }

    void clear() {
        queue_state.clear();
    }

private:
    std::deque<ElemId> queue_state;
};

using Backtracker = common::solver::Backtracker<QueueSimulator>;
} // namespace queue_cons
