#pragma once

#include "common/sampling.hpp"
#include "common/generator.hpp"
#include "cons.hpp"
#include <random>
#include <vector>

namespace queue_cons::generator {
using namespace common::generator;

/**
 * @brief Generates a sequence of queue operations based on the configuration
 *
 * @param config Generator configuration parameters
 * @return std::vector<QueueOp> Sequence of generated operations
 * @throws std::invalid_argument if configuration is invalid
 * @throws std::runtime_error if generation fails (e.g., queue underflow)
 */
[[nodiscard]] std::vector<QueueOp> generateQueueOps(const InputConfig& config,
                                                    URNG& rng);

} // namespace queue_cons::generator