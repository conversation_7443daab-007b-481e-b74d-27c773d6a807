#pragma once

#include "cons.hpp"
#include <optional>
#include <vector>

namespace queue_cons {

/**
 * Checks if the given history is valid for the queue operations.
 * 
 * @param ops Vector of queue operations
 * @param result Optional total history to check
 * @return true if the history is valid, false otherwise
 */
bool checkHistory(const std::vector<QueueOp>& ops,
                 const std::optional<TotalHistory>& result);

} // namespace queue_cons