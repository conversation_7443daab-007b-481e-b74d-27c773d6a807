#pragma once

#include "cons.hpp"
#include <string>

namespace queue_cons {

inline std::string toString(OpType op) {
    switch (op) {
    case OpType::ENQUEUE:
        return "ENQUEUE";
    case OpType::DEQUEUE:
        return "DEQUEUE";
    case OpType::PEEK_FRONT:
        return "PEEK_FRONT";
    case OpType::PEEK_BACK:
        return "PEEK_BACK";
    case OpType::EMPTY:
        return "EMPTY";
    }
    return "???";
}

} // namespace queue_cons
