/**
 * @file testing.cpp
 * @brief Implementation of testing functions for queue consistency checking
 *
 * This file contains the implementation of functions for testing queue
 * consistency checking algorithms.
 */

#include "testing.hpp"
#include "printer.hpp"
#include "common/sampling.hpp"
#include "common/testing_impl.hpp"
#include "generator.hpp"
#include "history_check.hpp"
#include <initializer_list>
#include <iomanip>
#include <thread>

namespace queue_cons {

using TestGenerator =
    common::testing::ParamCombinator<[](int trial, int max_ops, double variance,
                                        int permutation) {
      auto config =
          common::generator::InputConfig{.max_ops = max_ops,
                                         .variance_factor = variance,
                                         .permutation_factor = permutation};
      URNG rng(trial);
      return generator::generateQueueOps(config, rng);
    }>;

std::optional<std::vector<QueueOp>>
findCounterexample(const Solver& solver,
                   const common::generator::InputConfig& config,
                   int num_trials) {
  return common::testing::findCounterexample<QueueOp, TestGenerator>(
      solver, config, num_trials);
}

void benchmarkConfigurations(
    const Solver& solver, const common::testing::TestConfigParamList& config) {
  common::testing::benchmarkConfigurations<QueueOp, TestGenerator>(solver,
                                                                   config);
}

std::optional<std::vector<QueueOp>>
testSolverTime(const Solver& solver,
               const common::generator::InputConfig& config,
               std::chrono::milliseconds time_threshold, int num_trials) {
  return common::testing::testSolverTime<QueueOp, TestGenerator>(
      solver, config, time_threshold, num_trials);
}

std::optional<
    std::tuple<generator::InputConfig, std::vector<QueueOp>,
               std::optional<TotalHistory>, std::optional<TotalHistory>>>
compareSolvers(const Solver& solver1, const Solver& solver2,
               const common::testing::TestConfigParamList& config) {
  return common::testing::compareSolvers<QueueOp, TestGenerator>(
      solver1, solver2, config);
}

} // namespace queue_cons
