#include "common/history_check.hpp"
#include "cons.hpp"
#include <deque>

template <>
bool common::isOrderValid<queue_cons::QueueOp>(
    const std::vector<queue_cons::QueueOp>& ordered_ops) {
    std::deque<int> queue;
    for (const auto& op : ordered_ops) {
        switch (op.type) {
        case queue_cons::OpType::ENQUEUE:
            queue.push_back(op.element);
            break;
        case queue_cons::OpType::DEQUEUE:
            if (queue.empty() || queue.front() != op.element) {
                return false;
            }
            queue.pop_front();
            break;
        case queue_cons::OpType::PEEK_FRONT:
            if (queue.empty() || queue.front() != op.element) {
                return false;
            }
            break;
        case queue_cons::OpType::PEEK_BACK:
            if (queue.empty() || queue.back() != op.element) {
                return false;
            }
            break;
        case queue_cons::OpType::EMPTY:
            if (!queue.empty()) {
                return false;
            }
            break;
        }
    }
    return queue.empty();
}

namespace queue_cons {

bool checkHistory(const std::vector<QueueOp>& ops,
                 const std::optional<TotalHistory>& result) {
    return common::checkHistory<QueueOp>(ops, result);
}

} // namespace queue_cons
