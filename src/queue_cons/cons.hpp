#pragma once

#include <cassert>
#include <common/cons.hpp>
#include <common/interval.hpp>
#include <list>
#include <optional>
#include <string>
#include <unordered_map>
#include <vector>

namespace queue_cons {

using namespace common;

enum class OpType { ENQUEUE, DEQUEUE, PEEK_FRONT, PEEK_BACK, EMPTY };

struct QueueOp {
  OpId id;
  OpType type;
  ElemId element;
  Interval itv;

  Time lower() const { return itv.l; }
  Time upper() const { return itv.r; }

  QueueOp(OpId id, OpType type, ElemId element, Interval itv)
      : id(id), type(type), element(element), itv(itv) {
    assert(isValid(itv));
  }
};

struct ElemOps {
  QueueOp enqueue;
  QueueOp dequeue;
  std::vector<QueueOp> peeks_front;
  std::vector<QueueOp> peeks_back;

  Time rightmostFrontUpperBound;
  Time leftmostBackLowerBound;

  ElemOps(QueueOp enq, QueueOp deq, std::vector<QueueOp> pf, std::vector<QueueOp> pb)
      : enqueue(std::move(enq)), dequeue(std::move(deq)), 
        peeks_front(std::move(pf)), peeks_back(std::move(pb)) {}
};

using ElemOpsMap = std::unordered_map<ElemId, ElemOps>;

using HistoryConstructor = common::HistoryConstructor<QueueOp>;

/**
 * Filters operations by element and groups them into ElementOperations.
 *
 * @param operations The map of operations to filter.
 * @return A map of ElementOperations, keyed by element ID.
 */
std::unordered_map<ElemId, ElemOps>
filterOperationsByElement(const std::vector<QueueOp>& operations);

void tightenBounds(ElemOps& element_ops);

void tightenBounds(std::unordered_map<ElemId, ElemOps>& element_ops);

/* Checks if all enqueue/dequeue/peek intervals of an element are valid. */
[[nodiscard]] bool areItvsValid(const ElemOps& element_ops);

/* Checks if all enqueue/dequeue/peek intervals of all elements are valid. */
[[nodiscard]] bool
areItvsValid(const std::unordered_map<ElemId, ElemOps>& element_ops);

} // namespace queue_cons
