#include "common/printer.hpp"
#include "common/sampling.hpp"
#include "queue_cons/backtrack.hpp"
#include "queue_cons/generator.hpp"
#include "queue_cons/history_check.hpp"
#include "queue_cons/printer.hpp"
#include "stack_cons/backtrack.hpp"
#include "stack_cons/history_check.hpp"
#include "stack_cons/simple_dp.hpp"
#include "stack_cons/simple_top_down.hpp"
#include "stack_cons/testing.hpp"
#include <chrono>
#include <iomanip>
#include <random>

using namespace common::printer;

namespace stack_cons {
void tryFindCounterexample() {
  auto solver = [](const std::vector<stack_cons::StackOp>& ops) {
    return stack_cons::Backtracker(ops).constructHistory();
  };

  auto counterexample = findCounterexample(
      solver, {.max_ops = 5, .variance_factor = 2, .permutation_factor = 0},
      1000);

  if (counterexample) {
    std::cout << "Found counterexample:\n\n";
    std::cout << "Detailed StackOp List:\n";
    printOperations(*counterexample);

    std::cout << "\nOperation Timeline:\n";
    printOperationsWithTimeline(*counterexample);

    auto result = solver(*counterexample);
    if (result) {
      std::cout << "\nProposed (invalid) solution:\n";
      printResultWithTimeline(result, *counterexample);
    } else {
      std::cout << "\nSolver failed to find any solution.\n";
    }
  } else {
    std::cout << "No counterexample found.\n";
  }
}

void viewExample(int max_ops = 10, double variance_factor = 2,
                 int permutation_factor = 0) {
  URNG rng(42);
  common::generator::InputConfig config = {.max_ops = max_ops,
                                           .variance_factor = variance_factor,
                                           .permutation_factor =
                                               permutation_factor};

  std::cout << "Generated config: " << config << "\n";

  auto operations = stack_cons::generator::generateStackOps(config, rng);
  stack_cons::SimpleDP algo(operations);

  auto result = algo.constructHistory();

  if (result) {
    std::cout << "Solution found:\n";
    printResultWithTimeline(result, operations);
  } else {
    std::cout << "No solution found.\n";
    printOperationsWithTimeline(operations);
  }
}

void compareMySolvers() {
  auto solver1 = [](const std::vector<StackOp>& ops) {
    return Backtracker(ops).constructHistory();
  };
  auto solver2 = [](const std::vector<StackOp>& ops) {
    return SimpleTopDown(ops).constructHistory();
  };

  compareSolvers(solver1, solver2);
}

void runCustomConfig() {
  common::generator::InputConfig config = {
      .max_ops = 10, .variance_factor = 2, .permutation_factor = 0};
  URNG rng(42);

  auto operations = stack_cons::generator::generateStackOps(config, rng);
  printOperationsWithTimeline(operations);
  auto result = SimpleTopDown(operations).constructHistory();
  if (!result) {
    std::cout << "No solution found.\n";
    return;
  }

  if (!checkHistory(operations, result)) {
    std::cout << "INVALID solution found.\n";
  } else {
    std::cout << "Valid solution found.\n";
  }
  printResultWithTimeline(result, operations);
}
} // namespace stack_cons

namespace queue_cons {
void testSmallExample() {
  URNG rng(42);
  common::generator::InputConfig config = {
      .max_ops = 10, .variance_factor = 2, .permutation_factor = 0};

  std::cout << "Generated config: " << config << "\n";

  auto operations = queue_cons::generator::generateQueueOps(config, rng);
  auto solver = queue_cons::Backtracker(operations);

  std::cout << "\nQueue Operations:\n";
  common::printer::printOperationsWithTimeline(operations);

  auto result = solver.constructHistory();
  if (result) {
    std::cout << "\nSolution found:\n";
    common::printer::printResultWithTimeline(result, operations);

    if (!queue_cons::checkHistory(operations, result)) {
      std::cout << "\nWARNING: Invalid solution found!\n";
    } else {
      std::cout << "\nSolution verified as valid.\n";
    }
  } else {
    std::cout << "\nNo solution found.\n";
  }
}
} // namespace queue_cons

int main() {
  // stack_cons::tryFindCounterexample();
  // viewExample(10, 10, 10);

  // auto slow_case = stack_cons::testSolverTime(
  //     [](const std::vector<stack_cons::StackOp>& ops) {
  //       return stack_cons::SimpleDP(ops).constructHistory();
  //     });

  // if (slow_case) {
  //   std::cout << "\nSlow test case details:\n";
  //   printOperationsWithTimeline(*slow_case);
  // }

  // runCustomConfig();
  // stack_cons::compareMySolvers();
  // stack_cons::benchmarkConfigurations(
  //     [](const std::vector<stack_cons::StackOp>& ops) {
  //         return stack_cons::SimpleTopDown(ops).constructHistory();
  //     });

  // queue_cons::testSmallExample();

  return 0;
}
